<template>
  <div class="nav-header">
    <div class="logo-container">
      <h1 class="logo">智能体创建</h1>
    </div>
    <el-menu
      :default-active="activeIndex"
      class="nav-menu"
      mode="horizontal"
      background-color="#304156"
      text-color="#fff"
      active-text-color="#ffd04b"
      @select="handleMenuSelect"
    >
      <el-menu-item index="1">工作流</el-menu-item>
      <el-menu-item index="2">模型库</el-menu-item>
      <el-menu-item index="3">数据源</el-menu-item>
      <el-menu-item index="4">Agent</el-menu-item>
      <el-submenu index="5">
        <template slot="title">更多功能</template>
        <el-menu-item index="5-1">导入</el-menu-item>
        <el-menu-item index="5-2">导出</el-menu-item>
        <el-menu-item index="5-3">设置</el-menu-item>
      </el-submenu>
    </el-menu>
    <div class="action-buttons">
      <el-button
        type="primary"
        size="small"
        icon="el-icon-download"
        @click="handleSaveFlow"
        :loading="isSaving"
        :disabled="isSaving"
      >
        {{ isSaving ? '保存中...' : '保存' }}
      </el-button>
      <el-button type="success" size="small" icon="el-icon-video-play" @click="handleRunClick">运行</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'NavHeader',
  props: {
    isSaving: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      activeIndex: '1'
    };
  },
  methods: {
    handleMenuSelect(index) {
      this.activeIndex = index;
      
      // 根据选中的菜单项触发相应的事件
      switch (index) {
        case '1':
          this.$emit('menu-select', 'workflow');
          break;
        case '2':
          this.$emit('menu-select', 'models');
          break;
        case '3':
          this.$emit('menu-select', 'datasource');
          break;
        case '4':
          this.$emit('menu-select', 'agent');
          break;
        case '5-1':
          this.$emit('menu-select', 'import');
          break;
        case '5-2':
          this.$emit('menu-select', 'export');
          break;
        case '5-3':
          this.$emit('menu-select', 'settings');
          break;
      }
    },
    
    handleRunClick() {
      this.$emit('run-workflow');
    },

    handleSaveFlow() {
      this.$emit('save-flow');
    }
  }
}
</script>

<style scoped>
.nav-header {
  height: 60px;
  width: 100%;
  background-color: #304156;
  display: flex;
  align-items: center;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  position: relative;
  z-index: 10;
}

.logo-container {
  width: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo {
  color: #fff;
  font-size: 20px;
  margin: 0;
  font-weight: 600;
}

.nav-menu {
  flex: 1;
  border-bottom: none;
}

.action-buttons {
  padding-right: 20px;
  display: flex;
  gap: 10px;
}
</style> 
