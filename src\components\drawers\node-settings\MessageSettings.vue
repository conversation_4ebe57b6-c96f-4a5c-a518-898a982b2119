<template>
  <div class="message-settings">
    <div class="settings-section">
      <h3 class="section-title">基本设置</h3>
      <el-form :model="formData" ref="form" label-width="0" label-position="top" class="message-form">
        <el-form-item prop="name" :rules="[{ required: true, message: '请输入节点名称', trigger: 'blur' }]">
          <el-input v-model="formData.name" placeholder="请输入节点名称" class="name-input" />
        </el-form-item>
      </el-form>
    </div>
    <div class="settings-section">
      <h3 class="section-title">消息内容</h3>
      <el-form :model="formData" ref="formMsg" label-width="0" label-position="top" class="message-form">
        <el-form-item label="消息：" class="msg-label">
          <div v-for="(msg, idx) in formData.messages" :key="idx" class="message-item">
            <el-input
              type="textarea"
              v-model="formData.messages[idx]"
              :placeholder="'请输入您的消息内容，使用/快速插入变量。'"
              :rows="2"
              :autosize="{ minRows: 2, maxRows: 4 }"
              :class="{ 'is-error': showError && msg.trim() === '' }"
              @input="onInput"
            ></el-input>
            <el-button
              icon="el-icon-minus"
              type="text"
              @click="removeMessage(idx)"
              :disabled="formData.messages.length <= 1"
              class="del-btn"
            ></el-button>
            <div v-if="showError && msg.trim() === ''" class="error-tip">请输入消息或删除此字段。</div>
          </div>
          <el-button type="dashed" icon="el-icon-plus" @click="addMessage" class="add-btn">新增消息</el-button>
        </el-form-item>
        <el-form-item class="save-btn-row">
          <el-button type="primary" @click="handleSave" class="save-btn">保存设置</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MessageSettings',
  props: {
    nodeData: {
      type: Object,
      default: () => ({ form: { messages: [''] }, name: '' })
    }
  },
  data() {
    return {
      formData: {
        name: '',
        messages: ['']
      },
      showError: false
    };
  },
  watch: {
    nodeData: {
      handler(newVal) {
        this.formData.name = newVal.name || '';
        if (Array.isArray(newVal.form?.messages) && newVal.form.messages.length > 0) {
          this.formData.messages = [...newVal.form.messages];
        } else {
          this.formData.messages = [''];
        }
        this.showError = false;
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    addMessage() {
      this.formData.messages.push('');
    },
    removeMessage(idx) {
      if (this.formData.messages.length > 1) {
        this.formData.messages.splice(idx, 1);
      }
    },
    onInput() {
      if (this.showError) {
        if (this.formData.messages.every(msg => msg.trim() !== '')) {
          this.showError = false;
        }
      }
    },
    handleSave() {
      this.showError = false;
      const valid = this.formData.messages.every(msg => msg.trim() !== '');
      if (!this.formData.name.trim()) {
        this.$message.error('名称不能为空');
        return;
      }
      if (!valid) {
        this.showError = true;
        this.$message.error('请填写所有消息内容或删除空消息');
        return;
      }
      this.$emit('save', {
        name: this.formData.name,
        form: {
          messages: [...this.formData.messages]
        }
      });
    }
  }
};
</script>

<style scoped>
.message-settings {
  padding: 0 0 10px 0;
}
.settings-section {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 8px;
}
.section-title {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}
.message-form {
  padding: 0;
}
.name-input {
  margin-bottom: 10px;
}
.msg-label {
  font-weight: 500;
  color: #666;
  margin-bottom: 6px;
}
.message-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
}
.message-item .el-input {
  flex: 1;
  font-size: 14px;
}
.del-btn {
  margin-left: 8px;
  margin-top: 2px;
  color: #f56c6c;
}
.add-btn {
  margin-top: 6px;
  width: 100%;
  border-style: dashed;
}
.error-tip {
  color: #f56c6c;
  font-size: 12px;
  margin-top: 2px;
  margin-left: 2px;
}
.is-error textarea {
  border-color: #f56c6c !important;
}
.save-btn-row {
  text-align: right;
  margin-top: 18px;
}
.save-btn {
  min-width: 100px;
}
</style> 