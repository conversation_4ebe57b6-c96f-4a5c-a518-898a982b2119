# Ragflow Workflow Editor

一个基于Vue.js和X6的工作流编辑器，支持拖拽式节点创建和编辑。

## 功能特性

- 拖拽式节点创建
- 节点属性编辑
- 工作流可视化
- Agent管理
- 节点自动平移补丁

## 节点自动平移补丁

### 功能说明

节点自动平移补丁解决了以下问题：

1. **负坐标问题**：当DSL中的节点位置为负坐标时，自动将节点移动到正坐标区域
2. **位置偏移问题**：确保所有节点都在可见区域内
3. **自动居中**：将工作流自动居中显示在画布中

### 使用方法

#### 自动应用
- 在加载DSL时，补丁会自动应用
- 无需手动操作

#### 手动触发
- 点击工具栏中的"自动平移"按钮
- 点击工具栏中的"居中显示"按钮

### 补丁功能

1. **applyNodeAutoTranslation()**: 应用节点自动平移
   - 计算所有节点的边界
   - 检测负坐标区域
   - 自动调整节点位置

2. **centerWorkflow()**: 自动居中工作流
   - 计算工作流中心点
   - 计算画布中心点
   - 应用居中偏移

3. **calculateNodesBounds()**: 计算节点边界
   - 遍历所有节点
   - 计算最小和最大坐标

4. **translateAllNodes()**: 平移所有节点
   - 支持普通节点
   - 支持循环节点及其子节点
   - 支持循环内容区域

### 技术实现

- 使用X6的节点位置API
- 支持循环节点的特殊处理
- 包含错误处理和日志记录
- 提供用户友好的反馈信息

## 开发环境

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run serve

# 构建生产版本
npm run build
```

## 项目结构

```
src/
├── components/
│   ├── X6Graph.vue          # 主图表组件
│   ├── ModelSelector.vue     # 模型选择器
│   ├── AgentManagement.vue   # Agent管理
│   └── x6-utils/            # X6工具函数
├── api/                     # API封装
└── App.vue                  # 主应用组件
```

## 许可证

MIT License
