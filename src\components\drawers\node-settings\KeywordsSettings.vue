<template>
  <div class="keywords-settings">
    <div class="form-section">
      <h4>节点标题</h4>
      <el-input 
        v-model="nodeData.name" 
        placeholder="节点标题" 
        style="margin-bottom: 15px"
      ></el-input>
    </div>
    
    <div class="form-section">
      <h4>输入</h4>
      <div class="input-variable-box">
        <div v-for="(variable, index) in nodeData.variables" :key="index" class="variable-item">
          <el-tag size="medium">输入</el-tag>
          <span class="variable-name">{{ variable.name }}@{{ variable.source }}</span>
          <el-button 
            type="text" 
            icon="el-icon-delete" 
            class="delete-variable-btn"
            @click="deleteVariable(index)"
          ></el-button>
        </div>
        <div class="add-variable-button">
          <el-button type="text" icon="el-icon-plus" @click="showAddVariableDialog">新增变量</el-button>
        </div>
      </div>
    </div>
    
    <div class="form-section">
      <h4>选择使用的模型</h4>
      <el-select
        v-model="nodeData.form.llm_id"
        placeholder="选择模型"
        style="width: 100%"
        @change="handleModelChange"
      >
        <el-option label="qwen-max@Tongyi-Qianwen" value="qwen-max@Tongyi-Qianwen"></el-option>
        <el-option label="GPT-4" value="GPT-4"></el-option>
        <el-option label="Claude 3" value="Claude 3"></el-option>
        <el-option label="Llama 3" value="Llama 3"></el-option>
        <el-option label="Gemini" value="Gemini"></el-option>
      </el-select>
    </div>
    
    <div class="form-section">
      <h4>Top N</h4>
      <el-slider v-model="nodeData.form.top_n" :min="1" :max="10" :step="1"></el-slider>
    </div>



    <div class="form-section">
      <h4>温度 (Temperature)</h4>
      <el-switch v-model="nodeData.form.temperatureEnabled" style="margin-bottom: 10px;"></el-switch>
      <el-slider
        v-if="nodeData.form.temperatureEnabled"
        v-model="nodeData.form.temperature"
        :min="0"
        :max="2"
        :step="0.1"
        :disabled="!nodeData.form.temperatureEnabled"
      ></el-slider>
    </div>

    <div class="form-section">
      <h4>最大令牌数 (Max Tokens)</h4>
      <el-switch v-model="nodeData.form.maxTokensEnabled" style="margin-bottom: 10px;"></el-switch>
      <el-input-number
        v-if="nodeData.form.maxTokensEnabled"
        v-model="nodeData.form.max_tokens"
        :min="1"
        :max="4096"
        :disabled="!nodeData.form.maxTokensEnabled"
        style="width: 100%;"
      ></el-input-number>
    </div>
  </div>
</template>

<script>
export default {
  name: 'KeywordsSettings',
  props: {
    initialData: {
      type: Object,
      default: () => ({
        name: '关键词',
        selectedModel: '默认大模型',
        topN: 5,
        variables: [
          { name: 'deepseek-chat', source: 'DeepSeek' }
        ]
      })
    }
  },
  data() {
    return {
      nodeData: {
        name: '',
        selectedModel: '默认大模型',
        topN: 5,
        variables: []
      },
      showVariableDialog: false
    };
  },
  watch: {
    initialData: {
      handler(newVal) {
        this.nodeData = {
          name: newVal.name || '关键词',
          selectedModel: newVal.selectedModel || newVal.form?.llm_id || 'qwen-max@Tongyi-Qianwen',
          topN: newVal.topN || newVal.form?.top_n || 5,
          variables: Array.isArray(newVal.variables) ? [...newVal.variables] : [
            { name: 'deepseek-chat', source: 'DeepSeek' }
          ],
          form: {
            frequencyPenaltyEnabled: newVal.form?.frequencyPenaltyEnabled || false,
            frequency_penalty: newVal.form?.frequency_penalty || 0.7,
            llm_id: newVal.form?.llm_id || newVal.selectedModel || "qwen-max@Tongyi-Qianwen",
            maxTokensEnabled: newVal.form?.maxTokensEnabled || false,
            max_tokens: newVal.form?.max_tokens || 256,
            presencePenaltyEnabled: newVal.form?.presencePenaltyEnabled || false,
            presence_penalty: newVal.form?.presence_penalty || 0.4,
            query: newVal.form?.query || [
              {
                component_id: "Answer_0",
                type: "reference"
              }
            ],
            temperature: newVal.form?.temperature || 0.1,
            temperatureEnabled: newVal.form?.temperatureEnabled || false,
            topPEnabled: newVal.form?.topPEnabled || false,
            top_n: newVal.form?.top_n || newVal.topN || 5,
            top_p: newVal.form?.top_p || 0.3
          }
        };
      },
      immediate: true,
      deep: true
    },
    nodeData: {
      handler(newVal) {
        this.$emit('update', newVal);
      },
      deep: true
    }
  },
  methods: {
    handleModelChange(model) {
      // 同时更新 selectedModel 和 form.llm_id
      this.nodeData.selectedModel = model;
      this.nodeData.form.llm_id = model;
      this.$emit('model-change', model);
    },


    
    showAddVariableDialog() {
      this.$emit('show-variable-dialog');
    },
    
    deleteVariable(index) {
      this.$confirm('确定要删除此变量吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 从列表中删除
        this.nodeData.variables.splice(index, 1);
        
        this.$message({
          type: 'success',
          message: '变量已删除'
        });
      }).catch(() => {
        // 取消删除
      });
    },
    
    addVariable(variable) {
      this.nodeData.variables.push(variable);
    }
  }
}
</script>

<style scoped>
.form-section {
  margin-bottom: 24px;
}

.form-section h4 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 16px;
  color: #333;
}

/* 关键词节点相关样式 */
.input-variable-box {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 16px;
  background-color: #f9f9f9;
}

.variable-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  position: relative;
  padding: 5px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.variable-item:hover {
  background-color: #f0f0f0;
}

.variable-name {
  margin-left: 12px;
  font-size: 14px;
  color: #606266;
  flex-grow: 1;
}

.delete-variable-btn {
  padding: 2px;
  margin-left: 8px;
  color: #909399;
}

.delete-variable-btn:hover {
  color: #F56C6C;
}

.add-variable-button {
  display: flex;
  justify-content: center;
  margin-top: 8px;
}


</style> 