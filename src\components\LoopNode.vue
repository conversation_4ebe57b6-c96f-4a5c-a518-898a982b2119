<template>
  <div 
    class="loop-node" 
    :style="{ 
      backgroundColor: bgColor,
      width: width + 'px',
      height: height + 'px'
    }"
    :class="{ 'is-editing': isEditing }"
  >
    <div class="loop-header" @dblclick="startEditing">
      <!-- 循环图标 -->
      <div class="icon-wrapper" :style="{ backgroundColor: bgColor }">
        <svg class="icon-svg" :style="{ fill: iconColor }" viewBox="0 0 24 24" width="20" height="20">
          <path d="M12 4V1L8 5l4 4V6c3.31 0 6 2.69 6 6 0 1.01-.25 1.97-.7 2.8l1.46 1.46C19.54 15.03 20 13.57 20 12c0-4.42-3.58-8-8-8zm0 14c-3.31 0-6-2.69-6-6 0-1.01.25-1.97.7-2.8L5.24 7.74C4.46 8.97 4 10.43 4 12c0 4.42 3.58 8 8 8v3l4-4-4-4v3z" />
        </svg>
      </div>
      
      <!-- 标题区域 -->
      <div class="loop-title" v-if="!isEditing">{{ title }}</div>
      <input 
        v-else
        ref="titleInput"
        v-model="editTitle" 
        class="title-input"
        @blur="saveTitle"
        @keyup.enter="saveTitle"
        placeholder="输入标题"
      />
      
      <!-- 编辑按钮 -->
      <div class="edit-button" @click="startEditing" v-if="!isEditing">
        <svg viewBox="0 0 24 24" width="16" height="16">
          <path fill="#999" d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z" />
        </svg>
      </div>
      
      <!-- 菜单按钮 -->
      <div class="menu-button" @click="$emit('menu-click', $event)">⋮</div>
    </div>
    
    <!-- 循环节点内容区 -->
    <div class="loop-content" :class="{ 'empty': isEmpty }">
      <slot>
        <div class="placeholder-text">拖拽节点到此区域，形成循环</div>
      </slot>
    </div>
    
    <!-- 左右连接端口 -->
    <div class="port left-port">
      <div class="port-circle">+</div>
    </div>
    <div class="port right-port">
      <div class="port-circle">+</div>
    </div>
    
    <!-- 调整大小的控制点 -->
    <div class="resize-handle resize-handle-se" @mousedown.stop="handleResizeStart"></div>
  </div>
</template>

<script>
export default {
  name: 'LoopNode',
  props: {
    nodeId: {
      type: String,
      required: true
    },
    initialTitle: {
      type: String,
      default: '循环'
    },
    width: {
      type: Number,
      default: 400
    },
    height: {
      type: Number,
      default: 250
    },
    bgColor: {
      type: String,
      default: '#E8F7FF' // 浅蓝色背景
    },
    iconColor: {
      type: String,
      default: '#1890FF' // 蓝色图标
    },
    isEmpty: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      title: this.initialTitle,
      editTitle: this.initialTitle,
      isEditing: false,
      resizing: false,
      startX: 0,
      startY: 0,
      startWidth: this.width,
      startHeight: this.height
    };
  },
  methods: {
    startEditing() {
      this.isEditing = true;
      this.editTitle = this.title;
      this.$nextTick(() => {
        if (this.$refs.titleInput) {
          this.$refs.titleInput.focus();
        }
      });
    },
    saveTitle() {
      if (this.editTitle.trim()) {
        this.title = this.editTitle;
      }
      this.isEditing = false;
      this.$emit('update:title', this.title);
    },
    handleResizeStart(event) {
      // 阻止事件冒泡和默认行为
      event.stopPropagation();
      event.preventDefault();
      
      this.resizing = true;
      this.startX = event.clientX;
      this.startY = event.clientY;
      this.startWidth = this.width;
      this.startHeight = this.height;
      
      // 添加事件监听器
      document.addEventListener('mousemove', this.handleResize);
      document.addEventListener('mouseup', this.handleResizeEnd);
    },
    handleResize(event) {
      if (!this.resizing) return;
      
      const deltaX = event.clientX - this.startX;
      const deltaY = event.clientY - this.startY;
      
      const newWidth = Math.max(300, this.startWidth + deltaX); // 最小宽度300px
      const newHeight = Math.max(200, this.startHeight + deltaY); // 最小高度200px
      
      this.$emit('resize', { width: newWidth, height: newHeight });
    },
    handleResizeEnd() {
      this.resizing = false;
      document.removeEventListener('mousemove', this.handleResize);
      document.removeEventListener('mouseup', this.handleResizeEnd);
    }
  }
};
</script>

<style scoped>
.loop-node {
  position: relative;
  border-radius: 10px;
  border: 2px dashed #1890FF; /* 虚线边框 */
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  overflow: visible;
}

.loop-node:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.loop-header {
  display: flex;
  align-items: center;
  padding: 12px;
  position: relative;
  height: 40px;
}

.icon-wrapper {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 12px;
}

.loop-title {
  flex-grow: 1;
  font-weight: 500;
  color: #333;
  font-size: 16px;
}

.title-input {
  flex-grow: 1;
  border: none;
  border-bottom: 1px solid #1890FF;
  background-color: transparent;
  padding: 4px 0;
  font-size: 16px;
  font-weight: 500;
  outline: none;
}

.edit-button {
  opacity: 0;
  cursor: pointer;
  padding: 4px;
  margin-right: 8px;
  transition: all 0.2s;
}

.loop-node:hover .edit-button {
  opacity: 0.7;
}

.edit-button:hover {
  opacity: 1 !important;
}

.menu-button {
  cursor: pointer;
  font-size: 18px;
  font-weight: bold;
  color: #606266;
  padding: 0 6px;
}

.menu-button:hover {
  color: #333;
}

.loop-content {
  height: calc(100% - 40px);
  position: relative;
  padding: 10px;
}

.placeholder-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #999;
  font-size: 14px;
  text-align: center;
  pointer-events: none;
}

/* 连接端口样式 */
.port {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

.left-port {
  left: -10px;
}

.right-port {
  right: -10px;
}

.port-circle {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #fff;
  border: 1px solid #1890FF;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #1890FF;
  font-weight: bold;
  cursor: pointer;
}

/* 调整大小的控制点 */
.resize-handle {
  position: absolute;
  width: 10px;
  height: 10px;
  background-color: #1890FF;
  border-radius: 50%;
}

.resize-handle-se {
  right: -5px;
  bottom: -5px;
  cursor: nwse-resize;
}

.is-editing {
  box-shadow: 0 0 0 2px #1890FF;
}
</style> 