<template>
  <div class="retrieval-settings">
    <div class="settings-section">
      <h3 class="section-title">基本设置</h3>
      <el-form :model="formData" ref="form" label-width="0" label-position="top" class="retrieval-form">
        <el-form-item prop="name" :rules="[{ required: true, message: '请输入节点名称', trigger: 'blur' }]">
          <el-input v-model="formData.name" placeholder="请输入节点名称" class="name-input" />
        </el-form-item>
      </el-form>
    </div>
    <div class="settings-section">
      <h3 class="section-title">检索参数</h3>
      <el-form :model="formData" ref="formParams" label-width="120px" label-position="top" class="retrieval-form">
        <el-form-item label="知识库" prop="kb_ids" :rules="[{ required: true, message: '请选择至少一个知识库', trigger: 'change' }]">
          <el-select
            v-model="formData.kb_ids"
            multiple
            placeholder="请选择知识库"
            style="width: 100%"
            :loading="loadingDatasets"
            @focus="loadDatasets"
          >
            <el-option
              v-for="dataset in availableDatasets"
              :key="dataset.id"
              :label="dataset.name"
              :value="dataset.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="相似度权重" prop="keywords_similarity_weight">
          <el-input-number v-model="formData.keywords_similarity_weight" :min="0" :max="1" :step="0.01" />
        </el-form-item>
        <el-form-item label="相似度阈值" prop="similarity_threshold">
          <el-input-number v-model="formData.similarity_threshold" :min="0" :max="1" :step="0.01" />
        </el-form-item>
        <el-form-item label="Top N" prop="top_n">
          <el-input-number v-model="formData.top_n" :min="1" :max="100" :step="1" />
        </el-form-item>
        <el-form-item label="是否使用知识图谱" prop="use_kg">
          <el-switch v-model="formData.use_kg" />
        </el-form-item>
        <el-form-item label="查询来源 (query)" prop="query">
          <div v-for="(q, idx) in formData.query" :key="idx" class="query-item">
            <el-select
              v-model="q.component_id"
              placeholder="选择查询来源组件"
              class="query-select"
              filterable
            >
              <el-option
                v-for="comp in availableComponents"
                :key="comp.id"
                :label="`${comp.name} (${comp.id})`"
                :value="comp.id"
              />
            </el-select>
            <el-button
              type="danger"
              size="small"
              icon="el-icon-delete"
              class="del-btn"
              @click="removeQuery(idx)"
              v-if="formData.query.length > 1"
            />
          </div>
          <el-button type="primary" size="small" @click="addQuery">
            添加查询来源
          </el-button>
          <div class="form-item-tip">
            选择作为查询内容的组件，通常选择Answer组件作为查询来源
          </div>
        </el-form-item>

        <el-form-item label="输入来源 (kb_vars)" prop="kb_vars">
          <div v-for="(kv, idx) in formData.kb_vars" :key="idx" class="kb-var-item">
            <el-select
              v-model="formData.kb_vars[idx].component_id"
              placeholder="请选择输入来源组件"
              class="kb-var-select"
              @change="updateKbVarReference(idx)"
            >
              <el-option
                v-for="component in availableComponents"
                :key="component.id"
                :label="component.label"
                :value="component.id">
              </el-option>
            </el-select>
            <el-button icon="el-icon-minus" type="text" @click="removeKbVar(idx)" :disabled="formData.kb_vars.length <= 1" class="del-btn"></el-button>
          </div>
          <el-button type="dashed" icon="el-icon-plus" @click="addKbVar" class="add-btn">新增输入来源</el-button>
        </el-form-item>
        <el-form-item class="save-btn-row">
          <el-button type="primary" @click="handleSave" class="save-btn">保存设置</el-button>
          <el-button type="success" @click="testRetrieval" :loading="testingRetrieval" class="test-btn">测试检索</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import { listDatasets } from '@/api/dataset-management-API';
import { retrieveChunks } from '@/api/chunk-management-API';

export default {
  name: 'RetrievalSettings',
  props: {
    nodeData: {
      type: Object,
      default: () => ({ form: {}, name: '' })
    }
  },
  data() {
    return {
      formData: {
        name: '',
        kb_ids: [], // 知识库ID列表
        keywords_similarity_weight: 0.5, // 提高关键词权重
        similarity_threshold: 0.5, // 提高相似度阈值
        top_n: 3, // 减少返回结果数量
        use_kg: false,
        query: [{ component_id: '', type: 'reference' }], // 查询来源组件
        kb_vars: [] // 知识库变量来源（可以为空）
      },
      availableDatasets: [], // 可用的知识库列表
      availableComponents: [], // 可用的组件列表
      loadingDatasets: false, // 加载知识库的状态
      testingRetrieval: false // 测试检索的状态
    };
  },
  watch: {
    nodeData: {
      handler(newVal) {
        this.formData.name = newVal.name || '';
        const f = newVal.form || {};
        this.formData.kb_ids = Array.isArray(f.kb_ids) ? [...f.kb_ids] : [];
        this.formData.keywords_similarity_weight = typeof f.keywords_similarity_weight === 'number' ? f.keywords_similarity_weight : 0.3;
        this.formData.similarity_threshold = typeof f.similarity_threshold === 'number' ? f.similarity_threshold : 0.2;
        this.formData.top_n = typeof f.top_n === 'number' ? f.top_n : 8;
        this.formData.use_kg = typeof f.use_kg === 'boolean' ? f.use_kg : false;

        // 处理query数组 - query现在是组件选择形式
        if (Array.isArray(f.query) && f.query.length > 0) {
          this.formData.query = f.query.map(q => {
            if (typeof q === 'string') {
              // 兼容旧格式
              return { component_id: q, type: 'reference' };
            } else if (q && typeof q === 'object') {
              // 新格式
              return { component_id: q.component_id || '', type: q.type || 'reference' };
            }
            return { component_id: '', type: 'reference' };
          });
        } else {
          this.formData.query = [{ component_id: '', type: 'reference' }];
        }

        // 处理kb_vars数组
        if (Array.isArray(f.kb_vars) && f.kb_vars.length > 0) {
          this.formData.kb_vars = f.kb_vars.map(kv => {
            if (typeof kv === 'string') {
              // 兼容旧格式
              return { component_id: kv, type: 'reference' };
            } else if (kv && typeof kv === 'object') {
              // 新格式
              return { component_id: kv.component_id || '', type: kv.type || 'reference' };
            }
            return { component_id: '', type: 'reference' };
          });
        } else {
          this.formData.kb_vars = []; // kb_vars可以为空
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    // 加载知识库列表
    async loadDatasets() {
      if (this.availableDatasets.length > 0) {
        return; // 已经加载过了，不重复加载
      }

      this.loadingDatasets = true;
      try {
        console.log('[RetrievalSettings] 开始加载知识库列表');
        const response = await listDatasets({
          page: 1,
          page_size: 100 // 获取前100个知识库
        });

        console.log('[RetrievalSettings] 知识库API响应:', response);

        // 修复：根据实际API响应结构判断
        if (response.data && response.data.code === 0) {
          this.availableDatasets = response.data.data || [];
          console.log('[RetrievalSettings] 加载到的知识库:', this.availableDatasets);

          // 详细检查每个知识库的状态
          this.availableDatasets.forEach(dataset => {
            console.log(`[知识库状态] ${dataset.name}:`, {
              id: dataset.id,
              status: dataset.status,
              document_count: dataset.document_count,
              chunk_count: dataset.chunk_count,
              embedding_model: dataset.embedding_model,
              language: dataset.language
            });

            // 检查可能的问题
            if (dataset.status !== '1') {
              console.warn(`[知识库警告] ${dataset.name} 状态异常: ${dataset.status}`);
            }
            if (dataset.chunk_count === 0) {
              console.warn(`[知识库警告] ${dataset.name} 没有文档片段，可能未完成处理`);
            }
          });

          this.$message.success(`成功加载 ${this.availableDatasets.length} 个知识库`);
        } else {
          console.error('[RetrievalSettings] 加载知识库失败:', response.data);
          this.$message.error('加载知识库列表失败');
        }
      } catch (error) {
        console.error('[RetrievalSettings] 加载知识库出错:', error);
        this.$message.error('加载知识库列表出错');
      } finally {
        this.loadingDatasets = false;
      }
    },

    // 测试知识检索功能
    async testRetrieval() {
      if (!this.formData.kb_ids || this.formData.kb_ids.length === 0) {
        this.$message.error('请先选择知识库');
        return;
      }

      this.testingRetrieval = true;
      try {
        console.log('[检索测试] 开始测试知识检索');

        const testParams = {
          question: "售前", // 测试问题
          dataset_ids: this.formData.kb_ids,
          similarity_threshold: this.formData.similarity_threshold,
          vector_similarity_weight: 1 - this.formData.keywords_similarity_weight, // 注意：API参数是vector权重
          top_k: this.formData.top_n,
          keyword: true,
          highlight: true
        };

        console.log('[检索测试] 测试参数:', testParams);

        const response = await retrieveChunks(testParams);

        console.log('[检索测试] API响应:', response);

        if (response.data && response.data.code === 0) {
          const chunks = response.data.data?.chunks || [];
          console.log('[检索测试] 检索到的文档片段:', chunks);

          if (chunks.length > 0) {
            this.$message.success(`检索成功！找到 ${chunks.length} 个相关文档片段`);

            // 显示第一个片段的内容预览
            const firstChunk = chunks[0];
            console.log('[检索测试] 第一个片段内容:', firstChunk.content);
            console.log('[检索测试] 相似度分数:', firstChunk.similarity);
          } else {
            this.$message.warning('检索成功，但没有找到相关内容。请尝试调整相似度阈值或检查知识库内容。');
          }
        } else {
          console.error('[检索测试] API返回错误:', response.data);
          this.$message.error(`检索失败: ${response.data?.message || '未知错误'}`);
        }
      } catch (error) {
        console.error('[检索测试] 检索出错:', error);

        // 检查是否是pandas错误
        if (error.response?.data?.message?.includes('The truth value of a Series is ambiguous')) {
          this.$message.error('检测到pandas错误！这确认了问题出在知识库数据处理上。');
          console.error('[检索测试] 确认pandas错误:', error.response.data);
        } else {
          this.$message.error(`检索测试失败: ${error.message}`);
        }
      } finally {
        this.testingRetrieval = false;
      }
    },

    // 查询来源管理
    addQuery() {
      this.formData.query.push({ component_id: '', type: 'reference' });
    },
    removeQuery(index) {
      if (this.formData.query.length > 1) {
        this.formData.query.splice(index, 1);
      }
    },

    // 知识库变量管理
    addKbVar() {
      this.formData.kb_vars.push({ component_id: '', type: 'reference' });
    },
    removeKbVar(index) {
      if (this.formData.kb_vars.length > 1) {
        this.formData.kb_vars.splice(index, 1);
      }
    },
    updateKbVarReference(index) {
      // 确保引用格式正确
      if (this.formData.kb_vars[index]) {
        this.formData.kb_vars[index].type = 'reference';
      }
    },

    // 获取可用的组件列表
    loadAvailableComponents() {
      try {
        // 从父组件或全局状态获取工作流中的组件
        if (this.$parent && this.$parent.$parent && this.$parent.$parent.getGraphData) {
          const graphData = this.$parent.$parent.getGraphData();
          this.availableComponents = this.extractComponentsFromGraph(graphData);
        } else {
          // 如果无法获取图数据，提供默认选项
          this.availableComponents = [
            { id: 'begin', label: 'Begin (开始)' },
            { id: 'KeywordExtract_0', label: 'KeywordExtract (关键词提取)' },
            { id: 'Categorize_0', label: 'Categorize (分类)' },
            { id: 'Answer_0', label: 'Answer (回答)' }
          ];
        }
      } catch (error) {
        console.error('[RetrievalSettings] 获取可用组件失败:', error);
        this.availableComponents = [
          { id: 'begin', label: 'Begin (开始)' },
          { id: 'Answer_0', label: 'Answer (回答)' }
        ];
      }
    },

    // 从图数据中提取组件信息
    extractComponentsFromGraph(graphData) {
      const components = [];

      if (graphData && graphData.nodes) {
        graphData.nodes.forEach(node => {
          if (node.id && node.data) {
            const componentName = this.getComponentDisplayName(node.data.type || node.type);
            components.push({
              id: node.id,
              label: `${componentName} (${node.id})`
            });
          }
        });
      }

      return components;
    },

    // 获取组件显示名称
    getComponentDisplayName(type) {
      const typeMap = {
        'beginNode': 'Begin',
        'keywordNode': 'KeywordExtract',
        'keywords': 'KeywordExtract',
        'categorize': 'Categorize',
        'retrievalNode': 'Retrieval',
        'retrieval': 'Retrieval',
        'generation': 'Generate',
        'answer': 'Answer'
      };
      return typeMap[type] || type;
    },
    handleSave() {
      if (!this.formData.name.trim()) {
        this.$message.error('名称不能为空');
        return;
      }
      if (!this.formData.kb_ids || this.formData.kb_ids.length === 0) {
        this.$message.error('请选择至少一个知识库');
        return;
      }

      // 验证选择的知识库状态
      const selectedDatasets = this.availableDatasets.filter(dataset =>
        this.formData.kb_ids.includes(dataset.id)
      );

      for (const dataset of selectedDatasets) {
        if (dataset.status !== '1') {
          this.$message.error(`知识库"${dataset.name}"状态异常，请选择其他知识库`);
          return;
        }
        if (dataset.chunk_count === 0) {
          this.$message.warning(`知识库"${dataset.name}"没有文档片段，可能影响检索效果`);
        }
        console.log(`[知识库验证] 使用知识库: ${dataset.name} (${dataset.id}), 文档片段: ${dataset.chunk_count}`);
      }
      // 验证query引用（查询来源）
      if (this.formData.query.some(q => !q.component_id || !q.component_id.trim())) {
        this.$message.error('请选择所有查询来源组件或删除空项');
        return;
      }

      // 验证kb_vars引用（知识库变量）- 可以为空
      if (this.formData.kb_vars.length > 0 && this.formData.kb_vars.some(kv => !kv.component_id || !kv.component_id.trim())) {
        this.$message.error('请选择所有知识库变量组件或删除空项');
        return;
      }

      // 构建保存数据
      const saveData = {
        name: this.formData.name,
        form: {
          kb_ids: [...this.formData.kb_ids],
          keywords_similarity_weight: this.formData.keywords_similarity_weight,
          similarity_threshold: this.formData.similarity_threshold,
          top_n: this.formData.top_n,
          use_kg: this.formData.use_kg,
          query: [...this.formData.query], // 查询来源组件
          kb_vars: [...this.formData.kb_vars] // 知识库变量（可以为空）
        }
      };

      console.log('[Retrieval设置] 保存查询来源:', saveData.form.query);
      console.log('[Retrieval设置] 保存知识库变量:', saveData.form.kb_vars);

      this.$emit('save', saveData);
    }
  },

  // 组件挂载时预加载知识库列表和可用组件
  mounted() {
    this.loadDatasets();
    this.loadAvailableComponents();
  }
};
</script>

<style scoped>
.retrieval-settings {
  padding: 0 0 10px 0;
}
.settings-section {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 8px;
}
.section-title {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}
.retrieval-form {
  padding: 0;
}
.name-input {
  margin-bottom: 10px;
}
.query-item, .kb-var-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
}
.query-input {
  flex: 1;
  font-size: 14px;
}
.query-select, .kb-var-select {
  flex: 1;
  font-size: 14px;
}
.del-btn {
  margin-left: 8px;
  margin-top: 2px;
  color: #f56c6c;
}
.add-btn {
  margin-top: 6px;
  width: 100%;
  border-style: dashed;
}
.save-btn-row {
  text-align: right;
  margin-top: 18px;
}
.save-btn {
  min-width: 100px;
  margin-right: 10px;
}

.test-btn {
  min-width: 100px;
}
</style> 