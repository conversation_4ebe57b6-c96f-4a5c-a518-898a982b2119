/**
 * DSL调试和测试工具集
 * 从App.vue中提取的调试方法，用于诊断和修复DSL相关问题
 */

export class DSLDebugTools {
  constructor(x6GraphRef) {
    this.x6GraphRef = x6GraphRef;
  }

  /**
   * 创建最小化Retrieval测试
   */
  async createMinimalRetrievalTest() {
    console.group('🧪 创建最小化Retrieval测试');
    
    try {
      // 创建最简单的DSL结构
      const minimalDSL = {
        "answer": [],
        "components": {
          "begin": {
            "downstream": ["Retrieval_test"],
            "obj": {
              "component_name": "Begin",
              "output": {
                "content": {
                  "0": {
                    "content": "测试查询"
                  }
                }
              },
              "params": {
                "prologue": "测试查询"
              }
            },
            "upstream": []
          },
          "Retrieval_test": {
            "downstream": [],
            "obj": {
              "component_name": "Retrieval",
              "output": {
                "content": {
                  "0": ""
                },
                "reference": []
              },
              "params": {
                "kb_ids": ["请选择一个知识库ID"],
                "kb_vars": [
                  {
                    "component_id": "begin",
                    "type": "reference"
                  }
                ],
                "keywords_similarity_weight": 0.3,
                "similarity_threshold": 0.2,
                "top_n": 3,
                "use_kg": false,
                "rerank": false
              }
            },
            "upstream": ["begin"]
          }
        },
        "embed_id": "text-embedding-v2@Tongyi-Qianwen",
        "graph": {
          "edges": [],
          "nodes": []
        },
        "history": [],
        "messages": [],
        "path": [["begin"]],
        "reference": [],
        "rerank": false
      };
      
      console.log('🔬 最小化测试DSL:', JSON.stringify(minimalDSL, null, 2));
      console.log('⚠️ 请手动设置正确的kb_ids，然后测试这个最简单的结构');
      
      // 将DSL复制到剪贴板（如果支持）
      if (navigator.clipboard) {
        await navigator.clipboard.writeText(JSON.stringify(minimalDSL, null, 2));
        console.log('📋 DSL已复制到剪贴板');
      }
      
    } catch (error) {
      console.error('❌ 创建最小化测试失败:', error);
    }
    
    console.groupEnd();
  }

  /**
   * 尝试不同的Retrieval参数组合
   */
  async tryDifferentRetrievalParams() {
    console.group('🔧 尝试不同的Retrieval参数组合');
    
    const paramCombinations = [
      {
        name: '保守参数',
        params: {
          similarity_threshold: 0.1,
          top_n: 1,
          keywords_similarity_weight: 0.1,
          use_kg: false,
          rerank: false
        }
      },
      {
        name: '标准参数',
        params: {
          similarity_threshold: 0.2,
          top_n: 3,
          keywords_similarity_weight: 0.3,
          use_kg: false,
          rerank: false
        }
      },
      {
        name: '宽松参数',
        params: {
          similarity_threshold: 0.05,
          top_n: 5,
          keywords_similarity_weight: 0.5,
          use_kg: false,
          rerank: true
        }
      }
    ];
    
    paramCombinations.forEach((combo, index) => {
      console.log(`${index + 1}. ${combo.name}:`, combo.params);
    });
    
    console.log('💡 建议测试顺序：');
    console.log('1. 先用"保守参数"测试，看是否还有pandas错误');
    console.log('2. 如果保守参数正常，逐步增加复杂度');
    console.log('3. 检查知识库数据质量和格式');
    
    console.groupEnd();
  }

  /**
   * 应用pandas错误修复
   */
  async applyPandasErrorFix() {
    console.group('🔧 应用pandas错误修复');
    
    if (this.x6GraphRef) {
      const graphData = this.x6GraphRef.getGraphData();
      
      // 查找所有Retrieval组件
      const retrievalNodes = graphData.nodes.filter(node => 
        node.data && (node.data.label === 'Retrieval' || node.data.name === '知识检索')
      );
      
      if (retrievalNodes.length === 0) {
        console.log('未找到Retrieval组件');
        console.groupEnd();
        return;
      }
      
      console.log(`找到 ${retrievalNodes.length} 个Retrieval组件，开始应用修复`);
      
      retrievalNodes.forEach(node => {
        const nodeData = node.data;
        
        // 应用pandas错误修复参数
        const fixedParams = {
          ...nodeData.form,
          similarity_threshold: 0.05,  // 非常低的阈值
          top_n: 1,                    // 最少返回数量
          keywords_similarity_weight: 0.1,  // 最低权重
          vector_similarity_weight: 0.9,    // 主要依赖向量
          use_kg: false,               // 禁用知识图谱
          rerank: false                // 禁用重排序
        };
        
        console.log(`修复节点 ${node.id}:`, fixedParams);
        
        // 更新节点数据
        if (this.x6GraphRef.graph) {
          const x6Node = this.x6GraphRef.graph.getCellById(node.id);
          if (x6Node) {
            x6Node.setData({
              ...nodeData,
              form: fixedParams
            });
          }
        }
      });
      
      console.log('✅ pandas错误修复应用完成');
      console.log('💡 建议：现在重新测试工作流，看是否还有pandas错误');
    }
    
    console.groupEnd();
  }

  /**
   * 创建标准的混合检索-生成工作流
   */
  async createRetrievalGenerateWorkflow() {
    console.group('🔧 创建混合检索-生成工作流');
    
    if (this.x6GraphRef) {
      try {
        // 清空现有工作流
        this.x6GraphRef.clearWorkflow();
        
        // 等待清空完成
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 创建节点位置
        const positions = {
          begin: { x: 100, y: 200 },
          retrieval: { x: 400, y: 200 },
          generate: { x: 700, y: 200 },
          answer: { x: 1000, y: 200 }
        };
        
        // 创建Retrieval节点
        const retrievalNode = this.x6GraphRef.createNodeFromDrop({
          type: 'logicNode',
          data: {
            label: 'Retrieval',
            name: '知识检索',
            form: {
              kb_ids: [], // 用户需要手动选择知识库
              kb_vars: [{ component_id: 'begin', type: 'reference' }],
              similarity_threshold: 0.1,
              top_n: 3,
              keywords_similarity_weight: 0.1,
              vector_similarity_weight: 0.9,
              use_kg: false,
              rerank: false
            }
          }
        }, positions.retrieval);
        
        // 创建Generate节点
        const generateNode = this.x6GraphRef.createNodeFromDrop({
          type: 'generateNode',
          data: {
            label: 'Generate',
            name: '生成回答',
            form: {
              llm_id: 'qwen-max@Tongyi-Qianwen',
              prompt: `你是一个智能助手。请基于以下检索到的知识库内容来回答用户的问题。

检索到的相关内容：
{input}

请根据上述内容，准确、详细地回答用户的问题。如果检索内容不足以回答问题，请说明需要更多信息。`,
              temperature: 0.1,
              cite: true,
              message_history_window_size: 12
            }
          }
        }, positions.generate);
        
        // 创建Answer节点
        const answerNode = this.x6GraphRef.createNodeFromDrop({
          type: 'logicNode',
          data: {
            label: 'Answer',
            name: '对话'
          }
        }, positions.answer);
        
        // 等待节点创建完成
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 创建连接
        if (this.x6GraphRef.graph) {
          const beginNode = this.x6GraphRef.graph.getCellById('begin');
          
          if (beginNode && retrievalNode && generateNode && answerNode) {
            // Begin → Retrieval
            this.x6GraphRef.graph.addEdge({
              source: beginNode,
              target: retrievalNode,
              attrs: {
                line: {
                  stroke: 'rgb(202 197 245)',
                  strokeWidth: 2
                }
              }
            });
            
            // Retrieval → Generate
            this.x6GraphRef.graph.addEdge({
              source: retrievalNode,
              target: generateNode,
              attrs: {
                line: {
                  stroke: 'rgb(202 197 245)',
                  strokeWidth: 2
                }
              }
            });
            
            // Generate → Answer
            this.x6GraphRef.graph.addEdge({
              source: generateNode,
              target: answerNode,
              attrs: {
                line: {
                  stroke: 'rgb(202 197 245)',
                  strokeWidth: 2
                }
              }
            });
            
            console.log('✅ 混合检索-生成工作流创建完成');
            console.log('💡 请手动配置Retrieval组件的知识库');
          }
        }
        
      } catch (error) {
        console.error('❌ 创建工作流失败:', error);
      }
    }
    
    console.groupEnd();
  }

  /**
   * 诊断Retrieval组件的pandas错误
   */
  async diagnoseRetrievalPandasError() {
    console.group('🔍 专门诊断Retrieval组件pandas错误');
    
    if (this.x6GraphRef) {
      const graphData = this.x6GraphRef.getGraphData();
      const components = this.x6GraphRef.buildDSLFromGraph(graphData).components;
      
      console.log('🔍 开始诊断Retrieval组件配置...');
      
      // 查找所有Retrieval组件
      const retrievalComponents = Object.keys(components).filter(nodeId => 
        components[nodeId].obj.component_name === 'Retrieval'
      );
      
      if (retrievalComponents.length === 0) {
        console.log('ℹ️ 未找到Retrieval组件');
        console.groupEnd();
        return;
      }
      
      console.log(`📋 找到 ${retrievalComponents.length} 个Retrieval组件`);
      
      retrievalComponents.forEach(nodeId => {
        const component = components[nodeId];
        console.log(`\n🔍 检查组件: ${nodeId}`);
        console.log('参数配置:', component.obj.params);
        
        // 检查可能导致pandas错误的参数
        const issues = [];
        
        if (!component.obj.params.kb_ids || component.obj.params.kb_ids.length === 0) {
          issues.push('缺少知识库ID (kb_ids)');
        }
        
        if (!component.obj.params.query || component.obj.params.query.length === 0) {
          issues.push('缺少查询参数 (query)');
        } else {
          component.obj.params.query.forEach((q, index) => {
            if (!q.component_id || !q.type) {
              issues.push(`query[${index}] 格式不正确`);
            }
          });
        }
        
        if (component.obj.params.similarity_threshold < 0 || component.obj.params.similarity_threshold > 1) {
          issues.push('similarity_threshold 值不在有效范围 [0,1]');
        }
        
        if (component.obj.params.top_n <= 0) {
          issues.push('top_n 必须大于0');
        }
        
        if (issues.length === 0) {
          console.log('✅ 参数格式看起来正确');
        } else {
          console.log('❌ 发现问题:', issues);
        }
        
        // 检查上游连接
        if (!component.upstream || component.upstream.length === 0) {
          console.log('❌ 缺少上游连接');
        } else {
          console.log('✅ 上游连接:', component.upstream);
        }
        
        // 建议的修复参数
        console.log('💡 建议的安全参数:');
        console.log({
          similarity_threshold: 0.1,
          top_n: 1,
          keywords_similarity_weight: 0.1,
          vector_similarity_weight: 0.9,
          use_kg: false,
          rerank: false
        });
      });
    }
    
    console.groupEnd();
  }
}

// 导出单例实例创建函数
export function createDSLDebugTools(x6GraphRef) {
  return new DSLDebugTools(x6GraphRef);
}
