// OpenAI-Compatible API 封装
// 这里将封装与 OpenAI 兼容的 chat/completions、agents_openai 等接口
import ragflowRequest, { RAGFLOW_API_KEY } from './index';

/**
 * Create chat completion (OpenAI-Compatible)
 * @param {string} chatId - chat assistant id
 * @param {Object} data - { model, messages, stream }
 * @param {boolean} [isStream=false] - 是否流式返回
 * @returns {Promise}
 */
export function chatCompletionOpenAI(chatId, data, isStream = false) {
  return ragflowRequest.post(
    `/api/v1/chats_openai/${chatId}/chat/completions`,
    { ...data, stream: isStream },
    {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${RAGFLOW_API_KEY}`
      },
      responseType: isStream ? 'stream' : 'json'
    }
  );
}

/**
 * Create agent completion (OpenAI-Compatible)
 * @param {string} agentId - agent id
 * @param {Object} data - { model, messages, stream }
 * @param {boolean} [isStream=false] - 是否流式返回
 * @returns {Promise}
 */
export function agentCompletionOpenAI(agentId, data, isStream = false) {
  return ragflowRequest.post(
    `/api/v1/agents_openai/${agentId}/chat/completions`,
    { ...data, stream: isStream },
    {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${RAGFLOW_API_KEY}`
      },
      responseType: isStream ? 'stream' : 'json',
      timeout: isStream ? 120000 : 60000 // 流式输出使用2分钟超时，普通请求1分钟
    }
  );
}

// TODO: 可继续封装 agents_openai 相关API 