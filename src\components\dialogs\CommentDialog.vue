<template>
  <el-dialog
    :visible.sync="dialogVisible"
    :title="dialogTitle"
    width="500px"
    :close-on-click-modal="false"
  >
    <div class="comment-edit-form">
      <div class="node-description">
        添加说明性文字，不参与实际流程执行，用于记录设计思路或操作指南。
      </div>
      <el-form label-position="top">
        <el-form-item label="标题">
          <el-input v-model="currentComment.title" placeholder="输入标题"></el-input>
        </el-form-item>
        <el-form-item label="内容">
          <el-input
            type="textarea"
            v-model="currentComment.content"
            :rows="6"
            placeholder="输入注释内容..."
          ></el-input>
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSave">保存</el-button>
    </span>
  </el-dialog>
</template>

<script>
/**
 * 注释对话框组件
 * 
 * 添加说明性文字，不参与实际流程执行，用于记录设计思路或操作指南。
 */
export default {
  name: 'CommentDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    commentData: {
      type: Object,
      default: () => ({
        id: '',
        title: '',
        content: '',
        onSave: null
      })
    }
  },
  data() {
    return {
      currentComment: {
        id: '',
        title: '',
        content: '',
        onSave: null
      }
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(value) {
        if (!value) {
          this.$emit('close');
        }
      }
    },
    dialogTitle() {
      return this.currentComment.id ? '编辑注释' : '添加注释';
    }
  },
  watch: {
    commentData: {
      handler(newVal) {
        this.currentComment = { ...newVal };
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    handleSave() {
      console.log('Saving comment:', this.currentComment.title, this.currentComment.content);
      if (this.currentComment.onSave) {
        this.currentComment.onSave(this.currentComment.title, this.currentComment.content);
      }
      this.$emit('close');
    },
    handleCancel() {
      this.$emit('close');
    }
  }
}
</script>

<style scoped>
.comment-edit-form {
  padding: 0 20px;
}

.node-description {
  background-color: #FFFEF7;
  color: #666;
  padding: 10px 15px;
  border-radius: 4px;
  margin-bottom: 15px;
  border-left: 3px solid #FAAD14;
  font-size: 14px;
  line-height: 1.5;
}
</style> 