<template>
  <div class="comment-node" :class="{ 'is-editing': isEditing }" :style="{ backgroundColor: bgColor }">
    <div class="comment-header" @dblclick="startEditing">
      <svg class="icon-svg" :style="{ fill: iconColor }" viewBox="0 0 24 24" width="20" height="20">
        <path d="M14 17H4v2h10v-2zm6-8H4v2h16V9zM4 15h16v-2H4v2zM4 5v2h16V5H4z" />
      </svg>
      <div class="comment-title" v-if="!isEditing">{{ title }}</div>
      <input 
        v-else
        ref="titleInput"
        v-model="editTitle" 
        class="title-input"
        @blur="saveTitle"
        @keyup.enter="saveTitle"
        placeholder="输入标题"
      />
      <div class="edit-button" @click="startEditing" v-if="!isEditing">
        <svg viewBox="0 0 24 24" width="16" height="16">
          <path fill="#999" d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z" />
        </svg>
      </div>
    </div>
    <div class="comment-content">
      <textarea 
        v-model="content"
        placeholder="在此输入注释内容..."
        @blur="saveContent"
      ></textarea>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CommentNode',
  props: {
    nodeId: {
      type: String,
      required: true
    },
    initialTitle: {
      type: String,
      default: '注释'
    },
    initialContent: {
      type: String,
      default: ''
    },
    bgColor: {
      type: String,
      default: '#FFFBE6'
    },
    iconColor: {
      type: String,
      default: '#FAAD14'
    }
  },
  data() {
    return {
      title: this.initialTitle,
      editTitle: this.initialTitle,
      content: this.initialContent,
      isEditing: false
    };
  },
  methods: {
    startEditing() {
      this.isEditing = true;
      this.editTitle = this.title;
      this.$nextTick(() => {
        if (this.$refs.titleInput) {
          this.$refs.titleInput.focus();
        }
      });
    },
    saveTitle() {
      if (this.editTitle.trim()) {
        this.title = this.editTitle;
      }
      this.isEditing = false;
      this.$emit('update:title', this.title);
    },
    saveContent() {
      this.$emit('update:content', this.content);
    }
  }
};
</script>

<style scoped>
.comment-node {
  width: 250px;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
}

.comment-node:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.comment-header {
  display: flex;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  position: relative;
}

.icon-svg {
  margin-right: 12px;
  flex-shrink: 0;
}

.comment-title {
  flex-grow: 1;
  font-weight: 500;
  color: #333;
  font-size: 16px;
}

.title-input {
  flex-grow: 1;
  border: none;
  border-bottom: 1px solid #d9d9d9;
  background-color: transparent;
  padding: 4px 0;
  font-size: 16px;
  font-weight: 500;
  outline: none;
}

.title-input:focus {
  border-bottom-color: #1890ff;
}

.edit-button {
  opacity: 0;
  cursor: pointer;
  padding: 4px;
  transition: all 0.2s;
}

.comment-node:hover .edit-button {
  opacity: 0.7;
}

.edit-button:hover {
  opacity: 1 !important;
}

.comment-content {
  padding: 0;
}

.comment-content textarea {
  width: 100%;
  min-height: 80px;
  border: none;
  resize: vertical;
  padding: 12px;
  font-family: inherit;
  background-color: transparent;
  outline: none;
  color: #555;
}

.is-editing {
  box-shadow: 0 0 0 2px #1890ff;
}
</style> 