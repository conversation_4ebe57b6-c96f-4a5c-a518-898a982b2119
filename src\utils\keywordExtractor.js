/**
 * 通用智能关键词提取工具类
 * 支持多领域自适应关键词提取
 */

/**
 * 关键词提取器类
 */
class KeywordExtractor {
  constructor() {
    this.stopWords = [
      '的', '了', '在', '是', '有', '和', '就', '不', '人', '都', '一', '一个', 
      '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', 
      '好', '自己', '这', '那', '什么', '怎么', '为什么', '哪里', '时候', '地方',
      '方面', '情况', '问题', '事情', '东西', '内容', '信息', '资料', '可以', 
      '应该', '需要', '想要', '希望', '觉得', '认为', '知道', '了解', '清楚',
      '特别', '非常', '十分', '很多', '一些', '所有', '全部', '部分', '主要', 
      '重要', '请你', '帮我', '告诉', '给我', '让我', '使我', '令我', '对于', 
      '关于', '由于', '现在', '最近', '十分', '特别', '所以', '请你', '帮我'
    ];

    this.domainMaps = {
      // 商业/销售领域
      business: {
        keywords: ['销售', '售前', '售后', '营销', '客户', '服务', '产品', '方案', '合同', '订单', '价格', '成本', '利润', '市场'],
        weight: 1.2
      },
      // 技术/IT领域
      technology: {
        keywords: ['开发', '编程', '代码', '系统', '软件', '硬件', '网络', '数据库', '算法', '架构', '部署', '测试', '调试', 'API'],
        weight: 1.2
      },
      // 教育/培训领域
      education: {
        keywords: ['学习', '教育', '培训', '课程', '教学', '知识', '技能', '考试', '证书', '学校', '老师', '学生', '教材'],
        weight: 1.1
      },
      // 医疗/健康领域
      healthcare: {
        keywords: ['医疗', '健康', '疾病', '治疗', '药物', '医生', '患者', '症状', '诊断', '手术', '康复', '预防', '保健'],
        weight: 1.2
      },
      // 金融/财务领域
      finance: {
        keywords: ['金融', '财务', '投资', '理财', '银行', '贷款', '保险', '股票', '基金', '风险', '收益', '资产', '负债'],
        weight: 1.2
      },
      // 法律/合规领域
      legal: {
        keywords: ['法律', '法规', '合规', '合同', '协议', '条款', '权利', '义务', '责任', '诉讼', '仲裁', '律师', '法院'],
        weight: 1.1
      },
      // 管理/运营领域
      management: {
        keywords: ['管理', '运营', '流程', '制度', '规范', '标准', '质量', '效率', '团队', '领导', '决策', '计划', '执行'],
        weight: 1.1
      }
    };

    this.actionCategories = {
      // 信息获取类
      information: ['了解', '知道', '查询', '查看', '获取', '获得', '得到', '找到', '搜索', '寻找'],
      // 学习理解类
      learning: ['学习', '掌握', '理解', '明白', '弄清', '搞懂', '熟悉', '认识'],
      // 说明解释类
      explanation: ['说明', '解释', '描述', '介绍', '讲解', '阐述', '展示', '演示'],
      // 分析总结类
      analysis: ['分析', '总结', '归纳', '梳理', '整理', '比较', '对比', '评估'],
      // 操作执行类
      operation: ['操作', '执行', '实施', '进行', '开展', '推进', '落实', '完成'],
      // 帮助支持类
      assistance: ['帮助', '协助', '支持', '指导', '建议', '推荐', '提供', '给出']
    };

    this.patterns = [
      { regex: /(\w{2,4})的(\w{2,4})/g, extract: 1 },      // "XX的XX"模式，提取第一个词
      { regex: /(\w{2,4})事项/g, extract: 1 },              // "XX事项"
      { regex: /(\w{2,4})方面/g, extract: 1 },              // "XX方面"
      { regex: /(\w{2,4})问题/g, extract: 1 },              // "XX问题"
      { regex: /(\w{2,4})流程/g, extract: 1 },              // "XX流程"
      { regex: /(\w{2,4})管理/g, extract: 1 },              // "XX管理"
      { regex: /(\w{2,4})系统/g, extract: 1 },              // "XX系统"
      { regex: /(\w{2,4})平台/g, extract: 1 },              // "XX平台"
      { regex: /(\w{2,4})方案/g, extract: 1 },              // "XX方案"
      { regex: /(\w{2,4})策略/g, extract: 1 },              // "XX策略"
      { regex: /(\w{2,4})技术/g, extract: 1 },              // "XX技术"
      { regex: /(\w{2,4})方法/g, extract: 1 }               // "XX方法"
    ];
  }

  /**
   * 主要的关键词提取方法
   * @param {string} text - 要提取关键词的文本
   * @param {number} topN - 要提取的关键词数量
   * @returns {string} 提取的关键词字符串
   */
  extract(text, topN = 3) {
    console.log('[智能关键词提取] 开始通用智能关键词提取:', text);
    console.log('[智能关键词提取] 目标提取数量:', topN);
    
    if (!text || typeof text !== 'string') {
      return '';
    }
    
    // 提取用户输入部分（如果是对话格式）
    const userInput = this.extractUserInput(text);
    console.log('[智能关键词提取] 处理的用户输入:', userInput);
    
    const keywordCandidates = [];
    
    // 1. 领域自适应关键词提取
    const domainKeywords = this.extractDomainSpecificKeywords(userInput);
    keywordCandidates.push(...domainKeywords);
    
    // 2. 通用动作意图提取
    const actionKeywords = this.extractActionKeywords(userInput);
    keywordCandidates.push(...actionKeywords);
    
    // 3. 实体和概念提取
    const entityKeywords = this.extractEntityKeywords(userInput);
    keywordCandidates.push(...entityKeywords);
    
    // 4. 结构化模式提取
    const patternKeywords = this.extractPatternKeywords(userInput);
    keywordCandidates.push(...patternKeywords);
    
    // 5. 语义重要性评分和排序
    const scoredKeywords = this.scoreAndRankKeywords(keywordCandidates, userInput);
    
    // 6. 根据TopN参数选择最佳关键词
    const finalKeywords = scoredKeywords.slice(0, topN);
    
    const result = finalKeywords.join(', ');
    console.log('[智能关键词提取] 最终提取结果:', result);
    console.log('[智能关键词提取] 提取的关键词数量:', finalKeywords.length);
    console.log('[智能关键词提取] 关键词评分详情:', scoredKeywords.slice(0, topN * 2));
    
    return result;
  }

  /**
   * 提取用户输入部分
   * @param {string} text - 原始文本
   * @returns {string} 用户输入文本
   */
  extractUserInput(text) {
    // 如果包含多轮对话，提取最后一个USER输入和相关上下文
    const userMatches = text.match(/USER:([^]*?)(?=ASSISTANT:|$)/g);

    if (userMatches && userMatches.length > 1) {
      // 多轮对话，需要考虑上下文
      const lastUserInput = userMatches[userMatches.length - 1].replace('USER:', '').trim();
      const previousUserInput = userMatches[userMatches.length - 2]?.replace('USER:', '').trim();

      console.log('[关键词提取] 检测到多轮对话');
      console.log('[关键词提取] 上一轮用户输入:', previousUserInput);
      console.log('[关键词提取] 当前用户输入:', lastUserInput);

      // 如果当前输入很短，可能是追问，需要结合上下文
      if (lastUserInput.length < 15 && previousUserInput) {
        const combinedInput = `${previousUserInput} ${lastUserInput}`;
        console.log('[关键词提取] 合并上下文输入:', combinedInput);
        return combinedInput;
      }

      return lastUserInput;
    } else if (userMatches && userMatches.length === 1) {
      // 单轮对话
      const userMatch = text.match(/USER:(.+)$/s);
      return userMatch ? userMatch[1].trim() : text;
    } else {
      // 没有找到USER:标记，可能是纯文本输入
      console.log('[关键词提取] 未检测到对话格式，使用原始文本');
      return text.trim();
    }
  }

  /**
   * 构建完整的对话历史摘要
   * @param {string} text - 包含对话历史的文本
   * @returns {string} 对话历史摘要
   */
  buildConversationSummary(text) {
    const userMatches = text.match(/USER:([^]*?)(?=ASSISTANT:|$)/g);

    if (!userMatches || userMatches.length === 0) {
      return text;
    }

    // 构建对话摘要
    let summary = '对话历史摘要：\n';

    // 提取所有用户问题
    const userQuestions = userMatches.map(match =>
      match.replace('USER:', '').trim()
    );

    // 如果有多轮对话，总结主要话题
    if (userQuestions.length > 1) {
      summary += `用户共提出${userQuestions.length}个问题：\n`;
      userQuestions.forEach((question, index) => {
        summary += `${index + 1}. ${question}\n`;
      });

      // 识别话题演进
      const topics = this.extractTopicEvolution(userQuestions);
      if (topics.length > 0) {
        summary += `\n话题演进：${topics.join(' → ')}\n`;
      }
    }

    // 返回最后一个用户输入作为当前处理重点
    const currentInput = userQuestions[userQuestions.length - 1];
    summary += `\n当前处理重点：${currentInput}`;

    return summary;
  }

  /**
   * 提取话题演进
   * @param {Array} userQuestions - 用户问题数组
   * @returns {Array} 话题关键词数组
   */
  extractTopicEvolution(userQuestions) {
    const topics = [];

    userQuestions.forEach(question => {
      // 提取每个问题的核心话题
      const coreTopics = this.extractCoreTopics(question);
      topics.push(...coreTopics);
    });

    // 去重并保持顺序
    return [...new Set(topics)];
  }

  /**
   * 提取核心话题
   * @param {string} question - 单个问题
   * @returns {Array} 核心话题数组
   */
  extractCoreTopics(question) {
    const topics = [];

    // 业务阶段识别
    const businessPhases = ['售前', '售中', '售后', '销售', '营销', '客服'];
    businessPhases.forEach(phase => {
      if (question.includes(phase)) {
        topics.push(phase);
      }
    });

    // 动作意图识别
    const actions = ['总结', '分析', '介绍', '说明', '解释', '描述'];
    actions.forEach(action => {
      if (question.includes(action)) {
        topics.push(action);
      }
    });

    return topics;
  }

  /**
   * 领域自适应关键词提取
   * @param {string} text - 输入文本
   * @returns {Array} 领域关键词数组
   */
  extractDomainSpecificKeywords(text) {
    console.log('[领域识别] 开始领域自适应关键词提取');
    
    const detectedKeywords = [];
    let dominantDomain = null;
    let maxDomainScore = 0;
    
    // 检测主导领域
    for (const [domain, config] of Object.entries(this.domainMaps)) {
      let domainScore = 0;
      const foundKeywords = [];
      
      config.keywords.forEach(keyword => {
        if (text.includes(keyword)) {
          domainScore += config.weight;
          foundKeywords.push(keyword);
        }
      });
      
      if (domainScore > maxDomainScore) {
        maxDomainScore = domainScore;
        dominantDomain = domain;
      }
      
      detectedKeywords.push(...foundKeywords);
    }
    
    console.log('[领域识别] 检测到的主导领域:', dominantDomain);
    console.log('[领域识别] 领域关键词:', detectedKeywords);
    
    return [...new Set(detectedKeywords)];
  }

  /**
   * 通用动作意图提取
   * @param {string} text - 输入文本
   * @returns {Array} 动作关键词数组
   */
  extractActionKeywords(text) {
    const detectedActions = [];
    
    for (const [, actions] of Object.entries(this.actionCategories)) {
      actions.forEach(action => {
        if (text.includes(action)) {
          detectedActions.push(action);
        }
      });
    }
    
    console.log('[动作提取] 检测到的动作词汇:', detectedActions);
    return [...new Set(detectedActions)];
  }

  /**
   * 实体和概念提取
   * @param {string} text - 输入文本
   * @returns {Array} 实体关键词数组
   */
  extractEntityKeywords(text) {
    const entities = [];

    // 1. 提取复合概念（如"销售之后"、"售前阶段"等）
    const compoundConcepts = this.extractCompoundConcepts(text);
    entities.push(...compoundConcepts);

    // 2. 提取专有名词和重要概念（2-6字的中文词汇）
    const chineseWords = text.match(/[\u4e00-\u9fa5]{2,6}/g) || [];

    const filteredEntities = chineseWords.filter(word => {
      // 过滤掉常见的无意义词汇和语气词
      const meaninglessWords = [
        '现在我想', '我想要', '你帮我', '帮我总结', '总结一下', '这些信息',
        '特别是', '最近我', '对这个', '十分的', '感兴趣', '请帮我',
        '现在', '今天', '明天', '昨天', '这个', '那个', '什么', '怎么',
        '为什么', '哪里', '时候', '地方', '方面', '情况', '问题', '事情',
        '东西', '内容', '信息', '资料', '可以', '应该', '需要', '想要',
        '希望', '觉得', '认为', '知道', '了解', '清楚', '特别', '非常',
        '十分', '很多', '一些', '所有', '全部', '部分', '主要', '重要',
        '请你', '帮我', '告诉', '给我', '让我', '使我', '令我', '对于',
        '关于', '由于', '想要让', '帮我总结', '总结一些', '这些'
      ];

      return !this.isStopWord(word) &&
             !meaninglessWords.includes(word) &&
             !meaninglessWords.some(meaningless => word.includes(meaningless)) &&
             word.length >= 2 &&
             word.length <= 6 &&
             !this.isCommonPhrase(word);
    });

    entities.push(...filteredEntities);

    console.log('[实体提取] 检测到的实体:', entities);
    return [...new Set(entities)];
  }

  /**
   * 提取复合概念
   * @param {string} text - 输入文本
   * @returns {Array} 复合概念数组
   */
  extractCompoundConcepts(text) {
    const concepts = [];

    // 定义复合概念模式
    const compoundPatterns = [
      // 时间阶段相关
      /(\w+)之后/g,     // XX之后
      /(\w+)之前/g,     // XX之前
      /(\w+)期间/g,     // XX期间
      /(\w+)阶段/g,     // XX阶段
      /(\w+)过程/g,     // XX过程

      // 业务相关
      /(\w+)环节/g,     // XX环节
      /(\w+)步骤/g,     // XX步骤
      /(\w+)要点/g,     // XX要点
      /(\w+)重点/g,     // XX重点
      /(\w+)关键/g,     // XX关键

      // 完整的复合词
      /销售之后/g,
      /售前阶段/g,
      /售后服务/g,
      /客户关系/g,
      /产品介绍/g,
      /价格谈判/g,
      /合同签署/g,
      /项目实施/g,
      /技术支持/g,
      /用户培训/g
    ];

    compoundPatterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(text)) !== null) {
        if (match[0].length >= 3) { // 至少3个字符
          concepts.push(match[0]);
        }
        if (match[1] && match[1].length >= 2) { // 提取的核心词
          concepts.push(match[1]);
        }
      }
    });

    console.log('[复合概念提取] 检测到的复合概念:', concepts);
    return [...new Set(concepts)];
  }

  /**
   * 判断是否为常见短语
   * @param {string} word - 词汇
   * @returns {boolean} 是否为常见短语
   */
  isCommonPhrase(word) {
    const commonPhrases = [
      '想要', '需要', '希望', '觉得', '认为', '知道', '了解', '清楚',
      '可以', '应该', '能够', '必须', '一定', '肯定', '当然', '确实',
      '比较', '非常', '特别', '十分', '相当', '很多', '一些', '所有',
      '这样', '那样', '怎样', '如何', '什么', '哪些', '为什么', '怎么',
      '现在', '最近', '今天', '明天', '昨天', '以前', '以后', '将来',
      '帮我', '请你', '让我', '使我', '令我', '给我', '告诉', '说明'
    ];

    return commonPhrases.includes(word);
  }

  /**
   * 结构化模式提取
   * @param {string} text - 输入文本
   * @returns {Array} 模式关键词数组
   */
  extractPatternKeywords(text) {
    const patternKeywords = [];

    // 扩展的模式，包含更多时间和阶段相关的模式
    const enhancedPatterns = [
      ...this.patterns,
      // 时间阶段模式
      { regex: /(\w{2,4})之后/g, extract: 0 },      // "销售之后"整体提取
      { regex: /(\w{2,4})之前/g, extract: 0 },      // "销售之前"整体提取
      { regex: /(\w{2,4})期间/g, extract: 0 },      // "销售期间"整体提取
      { regex: /(\w{2,4})阶段/g, extract: 0 },      // "销售阶段"整体提取
      { regex: /(\w{2,4})过程/g, extract: 0 },      // "销售过程"整体提取

      // 业务环节模式
      { regex: /(\w{2,4})环节/g, extract: 0 },      // "销售环节"整体提取
      { regex: /(\w{2,4})步骤/g, extract: 0 },      // "销售步骤"整体提取
      { regex: /(\w{2,4})要点/g, extract: 0 },      // "销售要点"整体提取

      // 核心词提取（提取前面的主体）
      { regex: /(\w{2,4})之后/g, extract: 1 },      // 提取"销售"
      { regex: /(\w{2,4})之前/g, extract: 1 },      // 提取"销售"
      { regex: /(\w{2,4})阶段/g, extract: 1 },      // 提取"销售"
    ];

    enhancedPatterns.forEach(({ regex, extract }) => {
      let match;
      while ((match = regex.exec(text)) !== null) {
        const keyword = match[extract];
        if (keyword && keyword.length >= 2 && !this.isStopWord(keyword) && !this.isCommonPhrase(keyword)) {
          patternKeywords.push(keyword);
        }
      }
    });

    console.log('[模式提取] 检测到的模式关键词:', patternKeywords);
    return [...new Set(patternKeywords)];
  }

  /**
   * 关键词评分和排序
   * @param {Array} candidates - 候选关键词数组
   * @param {string} originalText - 原始文本
   * @returns {Array} 排序后的关键词数组
   */
  scoreAndRankKeywords(candidates, originalText) {
    const keywordScores = new Map();

    candidates.forEach(keyword => {
      let score = 0;

      // 1. 复合概念加分（如"销售之后"、"售前阶段"等）
      if (this.isCompoundConcept(keyword)) {
        score += 5; // 复合概念获得最高分
        console.log('[关键词评分] 复合概念加分:', keyword, '+5分');
      }

      // 2. 基础分数：词汇长度
      if (keyword.length >= 3 && keyword.length <= 5) {
        score += 3; // 3-5字的词汇更重要
      } else if (keyword.length >= 2 && keyword.length <= 4) {
        score += 2;
      } else if (keyword.length > 5) {
        score += 1;
      }

      // 3. 位置分数：出现在文本前半部分的词汇更重要
      const firstIndex = originalText.indexOf(keyword);
      const textLength = originalText.length;
      if (firstIndex < textLength * 0.3) {
        score += 2;
      } else if (firstIndex < textLength * 0.6) {
        score += 1;
      }

      // 4. 频率分数：出现次数
      const frequency = (originalText.match(new RegExp(keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g')) || []).length;
      score += Math.min(frequency, 3); // 最多加3分

      // 5. 语义重要性分数
      if (this.isNoun(keyword)) {
        score += 2;
      } else if (this.isVerb(keyword)) {
        score += 1;
      }

      // 6. 业务相关性加分
      if (this.isBusinessTerm(keyword)) {
        score += 2;
        console.log('[关键词评分] 业务术语加分:', keyword, '+2分');
      }

      // 7. 惩罚常见词汇
      if (this.isCommonPhrase(keyword)) {
        score -= 3;
        console.log('[关键词评分] 常见短语扣分:', keyword, '-3分');
      }

      // 累加分数（如果关键词重复出现）
      keywordScores.set(keyword, (keywordScores.get(keyword) || 0) + score);
    });

    // 按分数排序并返回关键词
    const sortedKeywords = Array.from(keywordScores.entries())
      .filter(([, score]) => score > 0) // 过滤掉负分的关键词
      .sort((a, b) => b[1] - a[1])
      .map(([keyword]) => keyword);

    console.log('[关键词评分] 评分结果:', Array.from(keywordScores.entries())
      .filter(([, score]) => score > 0)
      .sort((a, b) => b[1] - a[1]));

    return sortedKeywords;
  }

  /**
   * 判断是否为复合概念
   * @param {string} word - 词汇
   * @returns {boolean} 是否为复合概念
   */
  isCompoundConcept(word) {
    const compoundPatterns = [
      /\w+之后$/, /\w+之前$/, /\w+期间$/, /\w+阶段$/, /\w+过程$/,
      /\w+环节$/, /\w+步骤$/, /\w+要点$/, /\w+重点$/, /\w+关键$/
    ];

    return compoundPatterns.some(pattern => pattern.test(word)) ||
           word.length >= 4; // 4字以上的词汇通常是复合概念
  }

  /**
   * 判断是否为业务术语
   * @param {string} word - 词汇
   * @returns {boolean} 是否为业务术语
   */
  isBusinessTerm(word) {
    const businessTerms = [
      '销售', '售前', '售后', '营销', '客户', '服务', '产品', '方案',
      '合同', '订单', '价格', '成本', '利润', '市场', '推广', '宣传',
      '谈判', '签约', '实施', '培训', '支持', '维护', '管理', '流程',
      '注意事项', '要点', '重点', '关键', '技巧', '策略', '方法'
    ];

    return businessTerms.some(term => word.includes(term));
  }

  /**
   * 判断是否为停用词
   * @param {string} word - 词汇
   * @returns {boolean} 是否为停用词
   */
  isStopWord(word) {
    return this.stopWords.includes(word);
  }

  /**
   * 简单的词性判断 - 判断是否为名词
   * @param {string} word - 词汇
   * @returns {boolean} 是否为名词
   */
  isNoun(word) {
    const nounSuffixes = ['者', '员', '师', '家', '手', '人', '物', '品', '件', '具', '器', '机', '系统', '平台', '方案', '策略', '方法', '技术', '流程', '管理', '服务', '产品'];
    return nounSuffixes.some(suffix => word.endsWith(suffix));
  }

  /**
   * 简单的词性判断 - 判断是否为动词
   * @param {string} word - 词汇
   * @returns {boolean} 是否为动词
   */
  isVerb(word) {
    const verbSuffixes = ['了解', '学习', '掌握', '分析', '总结', '描述', '介绍', '说明', '解释', '帮助', '支持', '提供', '获取', '查询', '搜索'];
    return verbSuffixes.includes(word) || (word.length === 2 && ['学', '做', '用', '看', '听', '说', '写', '读', '想', '要'].some(v => word.includes(v)));
  }
}

// 创建单例实例
const keywordExtractor = new KeywordExtractor();

// 导出便捷方法
export function extractKeywords(text, topN = 3) {
  return keywordExtractor.extract(text, topN);
}

// 导出类（如果需要自定义配置）
export { KeywordExtractor };

// 默认导出
export default keywordExtractor;
