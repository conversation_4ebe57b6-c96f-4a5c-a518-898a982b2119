<template>
  <el-dialog
    :visible.sync="dialogVisible"
    :title="dialogTitle"
    width="600px"
    :close-on-click-modal="false"
  >
    <div class="classification-edit-form">
      <div class="node-description">
        对输入的问题进行分类，识别问题意图和类型，引导后续处理流程。
      </div>
      
      <div v-if="!editingCategory" class="categories-list">
        <div class="categories-header">
          <h3>分类列表</h3>
          <el-button type="primary" size="small" icon="el-icon-plus" @click="addNewCategory">新增分类</el-button>
        </div>
        
        <div v-if="categories.length === 0" class="empty-categories">
          <i class="el-icon-info"></i>
          <p>暂无分类，请添加新分类</p>
        </div>
        
        <el-collapse v-else v-model="activeCategories">
          <el-collapse-item v-for="(category, index) in categories" :key="index" :name="index">
            <template slot="title">
              <span class="category-title">{{ category.name }}</span>
            </template>
            <div class="category-content">
              <div class="category-info">
                <div class="info-item" v-if="category.description">
                  <div class="info-label">描述：</div>
                  <div class="info-value">{{ category.description }}</div>
                </div>
                <div class="info-item" v-if="category.example">
                  <div class="info-label">示例：</div>
                  <div class="info-value">{{ category.example }}</div>
                </div>
                <div class="info-item">
                  <div class="info-label">下一步：</div>
                  <div class="info-value">{{ getNextStepName(category.nextStep) }}</div>
                </div>
              </div>
              <div class="category-actions">
                <el-button type="text" icon="el-icon-edit" @click.stop="editCategory(index)">编辑</el-button>
                <el-button type="text" icon="el-icon-delete" @click.stop="deleteCategory(index)">删除</el-button>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
      
      <div v-else class="category-editor">
        <div class="editor-header">
          <h3>{{ isNewCategory ? '新增分类' : '编辑分类' }}</h3>
          <el-button icon="el-icon-back" @click="cancelEdit">返回</el-button>
        </div>
        
        <el-form :model="currentCategory" label-position="top">
          <el-form-item label="分类名称" required>
            <el-input v-model="currentCategory.name" placeholder="请输入分类名称"></el-input>
          </el-form-item>
          <el-form-item label="描述">
            <el-input 
              type="textarea" 
              v-model="currentCategory.description" 
              :rows="4"
              placeholder="请输入分类描述"
            ></el-input>
          </el-form-item>
          <el-form-item label="示例">
            <el-input 
              type="textarea" 
              v-model="currentCategory.example" 
              :rows="4"
              placeholder="请输入分类示例"
            ></el-input>
          </el-form-item>
          <el-form-item label="下一步">
            <el-select v-model="currentCategory.nextStep" placeholder="请选择下一步操作" style="width: 100%">
              <el-option 
                v-for="option in nextStepOptions" 
                :key="option.value" 
                :label="option.label" 
                :value="option.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        
        <div class="editor-actions">
          <el-button @click="cancelEdit">取消</el-button>
          <el-button type="primary" @click="saveCategory">保存</el-button>
        </div>
      </div>
    </div>
    
    <span slot="footer" class="dialog-footer" v-if="!editingCategory">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSave">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
/**
 * 问题分类对话框组件
 * 
 * 对输入的问题进行分类，识别问题意图和类型，引导后续处理流程。
 */
export default {
  name: 'ClassificationDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    classificationData: {
      type: Object,
      default: () => ({
        id: '',
        title: '',
        categories: [],
        onSave: null
      })
    }
  },
  data() {
    return {
      categories: [],
      activeCategories: [],
      editingCategory: false,
      isNewCategory: true,
      editingIndex: -1,
      currentCategory: {
        name: '',
        description: '',
        example: '',
        nextStep: ''
      },
      nextStepOptions: [
        { label: '知识检索', value: 'retrieval' },
        { label: '生成回答', value: 'generation' },
        { label: '对话', value: 'dialogue' },
        { label: '静态消息', value: 'message' },
        { label: '问题优化', value: 'optimization' },
        { label: '关键词', value: 'keywords' },
        { label: '条件', value: 'conditions' },
        { label: '集线器', value: 'hub' },
        { label: '模板转换', value: 'template' },
        { label: '循环', value: 'loop' },
        { label: '代码', value: 'code' }
      ]
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(value) {
        if (!value) {
          this.$emit('close');
        }
      }
    },
    dialogTitle() {
      return this.classificationData.id ? '编辑问题分类' : '添加问题分类';
    }
  },
  watch: {
    classificationData: {
      handler(newVal) {
        if (newVal.categories && Array.isArray(newVal.categories)) {
          this.categories = JSON.parse(JSON.stringify(newVal.categories));
        } else {
          this.categories = [];
        }
        
        // 重置编辑状态
        this.editingCategory = false;
        this.isNewCategory = true;
        this.editingIndex = -1;
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    handleSave() {
      console.log('保存分类设置:', this.categories);
      if (this.classificationData.onSave) {
        this.classificationData.onSave(this.classificationData.title, this.categories);
      }
      this.$emit('close');
    },
    handleCancel() {
      this.$emit('close');
    },
    addNewCategory() {
      this.editingCategory = true;
      this.isNewCategory = true;
      this.editingIndex = -1;
      this.currentCategory = {
        name: '',
        description: '',
        example: '',
        nextStep: 'retrieval'
      };
    },
    editCategory(index) {
      this.editingCategory = true;
      this.isNewCategory = false;
      this.editingIndex = index;
      this.currentCategory = JSON.parse(JSON.stringify(this.categories[index]));
    },
    deleteCategory(index) {
      this.$confirm('确定要删除该分类吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.categories.splice(index, 1);
        this.$message({
          type: 'success',
          message: '删除成功!'
        });
      }).catch(() => {
        // 取消删除
      });
    },
    cancelEdit() {
      this.editingCategory = false;
    },
    saveCategory() {
      if (!this.currentCategory.name) {
        this.$message.error('分类名称不能为空');
        return;
      }
      
      if (this.isNewCategory) {
        this.categories.push(JSON.parse(JSON.stringify(this.currentCategory)));
      } else {
        this.categories[this.editingIndex] = JSON.parse(JSON.stringify(this.currentCategory));
      }
      
      this.editingCategory = false;
      this.$message({
        type: 'success',
        message: this.isNewCategory ? '添加成功!' : '更新成功!'
      });
    },
    getNextStepName(value) {
      const option = this.nextStepOptions.find(opt => opt.value === value);
      return option ? option.label : '未设置';
    }
  }
}
</script>

<style scoped>
.classification-edit-form {
  padding: 0 20px;
}

.node-description {
  background-color: #FFF7E8;
  color: #666;
  padding: 10px 15px;
  border-radius: 4px;
  margin-bottom: 15px;
  border-left: 3px solid #F59A23;
  font-size: 14px;
  line-height: 1.5;
}

.categories-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.categories-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.empty-categories {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 0;
  color: #909399;
}

.empty-categories i {
  font-size: 32px;
  margin-bottom: 10px;
}

.category-title {
  font-weight: 500;
  color: #333;
}

.category-content {
  padding: 10px;
}

.category-info {
  margin-bottom: 10px;
}

.info-item {
  display: flex;
  margin-bottom: 8px;
}

.info-label {
  font-weight: 500;
  width: 60px;
  flex-shrink: 0;
}

.info-value {
  flex: 1;
  color: #606266;
  word-break: break-all;
}

.category-actions {
  display: flex;
  justify-content: flex-end;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.editor-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.editor-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}
</style> 