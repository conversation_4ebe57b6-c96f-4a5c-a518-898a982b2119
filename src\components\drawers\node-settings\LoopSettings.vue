<template>
  <div class="loop-settings">
    <el-collapse v-model="activeNames" class="section-collapse">
      <el-collapse-item name="input" class="section-item">
        <template #title>
          <span>输入</span>
        </template>
        <div class="input-section">
          <div class="variable-list">
            <div v-for="(variable, idx) in localForm.variables" :key="idx" class="variable-item">
              <el-input
                v-model="variable.name"
                placeholder="请输入变量名"
                size="small"
                :class="{'is-error': variableError(idx)}"
                @blur="validateVariable(idx)"
                style="width: 200px; margin-right: 8px;"
              />
              <el-button
                icon="el-icon-delete"
                type="text"
                @click="removeVariable(idx)"
                v-if="localForm.variables.length > 1"
              />
            </div>
            <el-button type="primary" plain icon="el-icon-plus" @click="addVariable" style="margin-top: 8px;">
              新增变量
            </el-button>
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>
    <div class="form-section">
      <el-form :model="localForm" ref="formRef" label-width="120px" :rules="rules">
        <el-form-item
          label="文本分段标识符"
          prop="delimiter"
          required
        >
          <el-tooltip content="用于分割输入文本的符号，如逗号、分号、换行等" placement="top">
            <el-select v-model="localForm.delimiter" placeholder="请选择分隔符" style="width: 200px;">
              <el-option label="逗号" value="," />
              <el-option label="分号" value=";" />
              <el-option label="换行" value="\n" />
              <el-option label="空格" value=" " />
              <el-option label="制表符" value="\t" />
            </el-select>
          </el-tooltip>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LoopSettings',
  props: {
    value: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      localForm: {
        variables: this.value.variables ? JSON.parse(JSON.stringify(this.value.variables)) : [{ name: '' }],
        delimiter: this.value.delimiter || ','
      },
      activeNames: ['input'],
      rules: {
        delimiter: [
          { required: true, message: '请选择文本分段标识符', trigger: 'change' }
        ]
      },
      variableErrors: []
    };
  },
  watch: {
    value: {
      handler(val) {
        this.localForm.variables = val.variables ? JSON.parse(JSON.stringify(val.variables)) : [{ name: '' }];
        this.localForm.delimiter = val.delimiter || ',';
      },
      deep: true
    },
    localForm: {
      handler(val) {
        this.$emit('input', { ...val });
      },
      deep: true
    }
  },
  methods: {
    addVariable() {
      this.localForm.variables.push({ name: '' });
    },
    removeVariable(idx) {
      this.localForm.variables.splice(idx, 1);
    },
    variableError(idx) {
      return this.variableErrors[idx];
    },
    validateVariable(idx) {
      const name = this.localForm.variables[idx].name;
      this.$set(this.variableErrors, idx, !name || name.trim() === '');
    }
  }
};
</script>

<style scoped>
.loop-settings {
  padding: 0 8px;
}
.section-collapse {
  margin-bottom: 16px;
}
.input-section {
  padding: 8px 0 0 0;
}
.variable-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.variable-item {
  display: flex;
  align-items: center;
}
.is-error .el-input__inner {
  border-color: #f56c6c;
}
.form-section {
  margin-top: 16px;
}
</style> 