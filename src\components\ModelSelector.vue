<template>
  <div class="model-selector">
    <div class="selector-content">
      <div 
        v-for="(item, index) in modelItems" 
        :key="index"
        class="model-item"
        draggable="true"
        @dragstart="handleDragStart($event, item)"
        @mouseenter="handleMouseEnter($event, item)"
        @mouseleave="handleMouseLeave"
      >
        <div class="icon-wrapper" :style="{ backgroundColor: item.bgColor }">
          <svg class="icon-svg" :style="{ fill: item.iconColor }" viewBox="0 0 24 24">
            <path :d="getSvgPath(item)"></path>
          </svg>
        </div>
        <span class="item-name">{{ item.name }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ModelSelector',
  data() {
    return {
      modelItems: [
        {
          id: 'knowledge_retrieval',
          name: '知识检索',
          icon: 'el-icon-search',
          type: 'retrieval',
          bgColor: '#E9F2FF',
          iconColor: '#3370FF',
          description: '从知识库中检索相关信息，支持语义搜索和关键词匹配，为回答提供依据。'
        },
        {
          id: 'generate_answer',
          name: '生成回答',
          icon: 'el-icon-plus',
          type: 'generation',
          bgColor: '#FFF1F0',
          iconColor: '#A84632',
          description: '根据输入的问题和上下文，生成完整、准确、有帮助的回答内容。'
        },
        {
          id: 'dialogue',
          name: '对话',
          icon: 'el-icon-arrow-right',
          type: 'dialogue',
          bgColor: '#FFF3E8',
          iconColor: '#FA701B',
          description: '管理多轮对话流程，保持上下文连贯性，实现自然的交互体验。'
        },
        {
          id: 'question_classification',
          name: '问题分类',
          icon: 'el-icon-document',
          type: 'classification',
          bgColor: '#FFF7E8',
          iconColor: '#F59A23',
          description: '对输入的问题进行分类，识别问题意图和类型，引导后续处理流程。'
        },
        {
          id: 'static_message',
          name: '静态消息',
          icon: 'el-icon-chat-dot-round',
          type: 'message',
          bgColor: '#E8FFF3',
          iconColor: '#00B578',
          description: '显示预设的静态消息，适用于欢迎语、引导提示或固定回复场景。'
        },
        {
          id: 'question_optimization',
          name: '问题优化',
          icon: 'el-icon-edit',
          type: 'optimization',
          bgColor: '#F5E8FF',
          iconColor: '#D54BF7',
          description: '优化用户输入的问题，纠正拼写错误、补充缺失信息或重新表述以提高理解准确度。'
        },
        {
          id: 'keywords',
          name: '关键词',
          icon: 'el-icon-connection',
          type: 'keywords',
          bgColor: '#EFE8FF',
          iconColor: '#722ED1',
          description: '提取文本中的关键词和实体，用于信息筛选、分类或标记。'
        },
        {
          id: 'conditions',
          name: '条件',
          icon: 'el-icon-sort',
          type: 'conditions',
          bgColor: '#F0E8FF',
          iconColor: '#8F2CFF',
          description: '根据设定的条件判断分支走向，实现流程的动态控制和决策。'
        },
        {
          id: 'hub',
          name: '集线器',
          icon: 'el-icon-menu',
          type: 'hub',
          bgColor: '#E8FFF8',
          iconColor: '#13C2C2',
          description: '连接多个节点，集中管理数据流向，简化复杂流程的连接关系。'
        },
        {
          id: 'template_conversion',
          name: '模板转换',
          icon: 'el-icon-refresh-right',
          type: 'template',
          bgColor: '#FFFBE8',
          iconColor: '#FADB14',
          description: '将内容转换为预定义的模板格式，标准化输出结构。'
        },
        {
          id: 'loop',
          name: '循环',
          icon: 'el-icon-refresh',
          type: 'loop',
          bgColor: '#E8F7FF',
          iconColor: '#1890FF',
          description: '重复执行特定操作，直到满足结束条件，用于批量处理或迭代改进。'
        },
        {
          id: 'code',
          name: '代码',
          icon: 'el-icon-tickets',
          type: 'code',
          bgColor: '#E8F3FF',
          iconColor: '#3491FA',
          description: '执行自定义代码片段，处理复杂逻辑或特殊功能，增强系统灵活性。'
        },
        // 添加注释节点，使用不同的样式
        {
          id: 'comment_node',
          name: '注释',
          icon: 'el-icon-document',
          type: 'comment',
          bgColor: '#FFFEF7',  // 更加淡的奶油色背景
          iconColor: '#FAAD14', // 金色图标
          isSpecial: true,      // 标记为特殊节点
          description: '添加说明性文字，不参与实际流程执行，用于记录设计思路或操作指南。'
        }
      ]
    };
  },
  methods: {
    getSvgPath(item) {
      // 定义SVG路径映射，与X6Graph.vue中保持一致
      const iconMap = {
        // 基本图标
        'el-icon-search': 'M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z',
        'el-icon-plus': 'M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6z',
        'el-icon-arrow-right': 'M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z',
        'el-icon-document': 'M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 1.99 2H18c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z',
        'el-icon-chat-dot-round': 'M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zM6 9h12v2H6V9zm8 5H6v-2h8v2zm4-6H6V6h12v2z',
        'el-icon-edit': 'M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z',
        'el-icon-connection': 'M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .24.04.47.09.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92 1.61 0 2.92-1.31 2.92-2.92s-1.31-2.92-2.92-2.92z',
        'el-icon-sort': 'M3 18h6v-2H3v2zM3 6v2h18V6H3zm0 7h12v-2H3v2z',
        'el-icon-menu': 'M4 18h16c1.1 0 2-.9 2-2v-1.8c0-.3-.2-.6-.5-.6s-.5.2-.5.5v1.9c0 .6-.4 1-1 1H4c-.6 0-1-.4-1-1v-1.8c0-.3-.2-.5-.5-.5s-.5.2-.5.6V16c0 1.1.9 2 2 2zM4 13h16c1.1 0 2-.9 2-2V9.2c0-.3-.2-.5-.5-.5s-.5.2-.5.5V11c0 .6-.4 1-1 1H4c-.6 0-1-.4-1-1V9.2c0-.3-.2-.5-.5-.5s-.5.2-.5.5V11c0 1.1.9 2 2 2zm-2-7c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2s-.9-2-2-2H4c-1.1 0-2 .9-2 2z',
        'el-icon-refresh-right': 'M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z',
        'el-icon-refresh': 'M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z',
        'el-icon-tickets': 'M22 10V6c0-1.11-.9-2-2-2H4c-1.1 0-1.99.89-1.99 2v4c1.1 0 1.99.9 1.99 2s-.89 2-2 2v4c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2v-4c-1.1 0-2-.9-2-2s.9-2 2-2zm-2-1.46c-.43.35-1 .56-1.62.56H5.62c-.62 0-1.19-.21-1.62-.56V6.04c.43-.35 1-.56 1.62-.56h12.76c.62 0 1.19.21 1.62.56v2.5zM5.62 18.96c-.62 0-1.19-.21-1.62-.56v-2.5c.43-.35 1-.56 1.62-.56h12.76c.62 0 1.19.21 1.62.56v2.5c-.43.35-1 .56-1.62.56H5.62z',
        'el-icon-video-play': 'M8 5v14l11-7z',
        'el-icon-caret-right': 'M10 17l5-5-5-5v10z',
      };
      
      // 基于节点类型返回路径
      switch (item.type) {
        case 'hub':
          return 'M20 13H4c-.55 0-1 .45-1 1v6c0 .55.45 1 1 1h16c.55 0 1-.45 1-1v-6c0-.55-.45-1-1-1zm-1 6H5v-4h14v4zM20 3H4c-.55 0-1 .45-1 1v6c0 .55.45 1 1 1h16c.55 0 1-.45 1-1V4c0-.55-.45-1-1-1zm-1 6H5V5h14v4z';
        case 'conditions':
          return 'M14 6l-4.22 5.63 1.25 1.67L14 9.33 19 16h-8.46l-4.01-5.37L1 18h22L14 6zM5 16l1.52-2.03L8.04 16H5z';
        case 'keywords':
          return 'M21.41 11.58l-9-9C12.05 2.22 11.55 2 11 2H4c-1.1 0-2 .9-2 2v7c0 .55.22 1.05.59 1.42l9 9c.36.36.86.58 1.41.58s1.05-.22 1.41-.59l7-7c.37-.36.59-.86.59-1.41s-.23-1.06-.59-1.42zM5.5 7C4.67 7 4 6.33 4 5.5S4.67 4 5.5 4 7 4.67 7 5.5 6.33 7 5.5 7z';
        case 'code':
          return 'M9.4 16.6L4.8 12l4.6-4.6L8 6l-6 6 6 6 1.4-1.4zm5.2 0l4.6-4.6-4.6-4.6L16 6l6 6-6 6-1.4-1.4z';
        case 'loop':
          return 'M12 4V1L8 5l4 4V6c3.31 0 6 2.69 6 6 0 1.01-.25 1.97-.7 2.8l1.46 1.46C19.54 15.03 20 13.57 20 12c0-4.42-3.58-8-8-8zm0 14c-3.31 0-6-2.69-6-6 0-1.01.25-1.97.7-2.8L5.24 7.74C4.46 8.97 4 10.43 4 12c0 4.42 3.58 8 8 8v3l4-4-4-4v3z';
        case 'message':
          return 'M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 14H6v-2h12v2zm0-3H6v-2h12v2zm0-3H6V8h12v2z';
        case 'comment':
          return 'M14 17H4v2h10v-2zm6-8H4v2h16V9zM4 15h16v-2H4v2zM4 5v2h16V5H4z';
        default:
          return iconMap[item.icon] || 'M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z';
      }
    },
    
    handleDragStart(event, item) {
      try {
        // 创建一个干净的对象副本，确保不含特殊字符
        const cleanItem = { ...item };
        
        // 特殊处理menuButton文本，防止JSON序列化错误
        if (cleanItem.menuText === '⋮') {
          cleanItem.menuText = 'menu';
        }
        
        // 序列化对象为JSON字符串
        const jsonString = JSON.stringify(cleanItem);
        console.log('Drag start with data:', jsonString);
        
        // 确保JSON字符串是有效的
        if (!jsonString || jsonString === '{}' || jsonString === 'null') {
          console.error('无效的拖拽数据');
          return;
        }
        
        // 设置拖拽数据 - 添加多种MIME类型以提高兼容性
        event.dataTransfer.setData('application/json', jsonString);
        event.dataTransfer.setData('text/plain', jsonString);
        event.dataTransfer.effectAllowed = 'copy';
        
        // 创建拖拽时的视觉反馈
        const dragImage = document.createElement('div');
        dragImage.classList.add('drag-image');
        dragImage.textContent = item.name;
        dragImage.style.cssText = 'position:absolute; top:-1000px; background:#f0f0f0; color:#333; padding:5px 10px; border-radius:4px; border:1px solid #ccc;';
        document.body.appendChild(dragImage);
        event.dataTransfer.setDragImage(dragImage, 30, 15);
        
        setTimeout(() => {
          document.body.removeChild(dragImage);
        }, 0);
        
        // 触发自定义事件
        this.$emit('drag-start', cleanItem);
      } catch (error) {
        console.error('拖拽初始化错误:', error);
      }
    },
    
    // 处理鼠标进入事件
    handleMouseEnter(event, item) {
      // 计算提示框位置
      const rect = event.currentTarget.getBoundingClientRect();
      
      // 发出事件到父组件
      this.$emit('show-tooltip', {
        title: item.name,
        content: item.description,
        style: {
          top: `${rect.top + rect.height/2}px`, // 垂直居中对齐
          left: `${rect.right + 15}px`, // 在右侧显示，留出足够间距给箭头
          transform: 'translateY(-50%)', // 使提示框垂直居中
        }
      });
      
      console.log('ModelSelector: 鼠标进入', item.name);
    },
    
    // 处理鼠标离开事件
    handleMouseLeave() {
      // 发出事件到父组件
      this.$emit('hide-tooltip');
      
      console.log('ModelSelector: 鼠标离开');
    }
  }
};
</script>

<style scoped>
.model-selector {
  width: 100%;
  height: calc(100vh - 60px);
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  overflow-y: auto;
  position: relative;
  z-index: 5;
  padding: 12px 8px;
}

.selector-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.model-item {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  background-color: #fff;
  border-radius: 6px;
  cursor: grab;
  border: 1px solid #eaeaea;
  transition: all 0.2s ease;
}

.model-item:hover {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.icon-wrapper {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 10px;
  flex-shrink: 0;
}

.icon-svg {
  width: 20px;
  height: 20px;
}

.item-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style> 