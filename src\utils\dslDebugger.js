/**
 * DSL调试工具
 * 用于调试和验证DSL配置的正确性
 */

/**
 * 验证DSL组件的完整性
 * @param {Object} components - DSL组件配置
 * @returns {Object} 验证结果
 */
export function validateDSLComponents(components) {
  const validation = {
    isValid: true,
    errors: [],
    warnings: [],
    stats: {
      totalComponents: 0,
      componentsWithInputs: 0,
      componentsWithOutputs: 0,
      componentsWithConversationHistory: 0
    }
  };

  if (!components || typeof components !== 'object') {
    validation.isValid = false;
    validation.errors.push('DSL组件配置为空或格式错误');
    return validation;
  }

  validation.stats.totalComponents = Object.keys(components).length;

  Object.entries(components).forEach(([nodeId, component]) => {
    // 检查基本结构
    if (!component.obj || !component.obj.component_name) {
      validation.errors.push(`节点 ${nodeId} 缺少基本组件信息`);
      validation.isValid = false;
      return;
    }

    const componentName = component.obj.component_name;
    const inputs = component.obj.inputs || [];
    const output = component.obj.output;

    // 统计输入输出
    if (inputs.length > 0) {
      validation.stats.componentsWithInputs++;
    }
    if (output && Object.keys(output).length > 0) {
      validation.stats.componentsWithOutputs++;
    }

    // 检查对话历史
    const hasConversationHistory = inputs.some(input => 
      input.content && (
        input.content.includes('USER:') || 
        input.content.includes('ASSISTANT:')
      )
    );
    if (hasConversationHistory) {
      validation.stats.componentsWithConversationHistory++;
    }

    // 特定组件类型的验证
    switch (componentName) {
      case 'KeywordExtract':
        if (!hasConversationHistory) {
          validation.warnings.push(`关键词提取节点 ${nodeId} 缺少对话历史输入`);
        }
        break;
      
      case 'Retrieval':
        if (inputs.length === 0) {
          validation.warnings.push(`检索节点 ${nodeId} 缺少输入数据`);
        }
        break;
      
      case 'Generate':
        if (inputs.length === 0) {
          validation.warnings.push(`生成节点 ${nodeId} 缺少输入数据`);
        }
        break;
      
      case 'Answer':
        if (!output || !output.content) {
          validation.warnings.push(`回答节点 ${nodeId} 缺少输出内容`);
        }
        break;
    }
  });

  return validation;
}

/**
 * 生成DSL保存报告
 * @param {Object} components - DSL组件配置
 * @param {string} conversationHistory - 对话历史
 * @param {string} sessionId - 会话ID
 * @returns {Object} 保存报告
 */
export function generateSaveReport(components, conversationHistory, sessionId) {
  const validation = validateDSLComponents(components);
  
  const report = {
    timestamp: new Date().toISOString(),
    sessionId,
    conversationHistory: {
      hasHistory: !!conversationHistory,
      length: conversationHistory ? conversationHistory.length : 0,
      messageCount: conversationHistory ? 
        conversationHistory.split('\n').filter(line => 
          line.startsWith('USER:') || line.startsWith('ASSISTANT:')
        ).length : 0
    },
    validation,
    summary: {
      totalNodes: validation.stats.totalComponents,
      nodesWithData: validation.stats.componentsWithOutputs,
      nodesWithConversation: validation.stats.componentsWithConversationHistory,
      isComplete: validation.isValid && validation.stats.componentsWithConversationHistory > 0
    }
  };

  return report;
}

/**
 * 打印DSL调试信息
 * @param {Object} components - DSL组件配置
 * @param {string} conversationHistory - 对话历史
 * @param {string} sessionId - 会话ID
 */
export function debugDSLSave(components, conversationHistory, sessionId) {
  const report = generateSaveReport(components, conversationHistory, sessionId);
  
  console.group('🔍 DSL保存调试报告');
  console.log('📊 保存统计:', report.summary);
  console.log('💬 对话历史:', report.conversationHistory);
  console.log('✅ 验证结果:', report.validation);
  
  if (report.validation.errors.length > 0) {
    console.error('❌ 错误:', report.validation.errors);
  }
  
  if (report.validation.warnings.length > 0) {
    console.warn('⚠️ 警告:', report.validation.warnings);
  }
  
  console.groupEnd();
  
  return report;
}

/**
 * 检查DSL数据的对话历史完整性
 * @param {Object} components - DSL组件配置
 * @returns {Object} 检查结果
 */
export function checkConversationIntegrity(components) {
  const result = {
    hasKeywordExtractWithHistory: false,
    hasRetrievalWithHistory: false,
    missingHistoryNodes: [],
    conversationSources: []
  };

  Object.entries(components).forEach(([nodeId, component]) => {
    const componentName = component.obj.component_name;
    const inputs = component.obj.inputs || [];
    
    const hasConversationHistory = inputs.some(input => 
      input.content && (
        input.content.includes('USER:') || 
        input.content.includes('ASSISTANT:')
      )
    );

    if (hasConversationHistory) {
      result.conversationSources.push(nodeId);
    }

    switch (componentName) {
      case 'KeywordExtract':
        if (hasConversationHistory) {
          result.hasKeywordExtractWithHistory = true;
        } else {
          result.missingHistoryNodes.push(nodeId);
        }
        break;
      
      case 'Retrieval':
        if (hasConversationHistory) {
          result.hasRetrievalWithHistory = true;
        } else {
          result.missingHistoryNodes.push(nodeId);
        }
        break;
    }
  });

  return result;
}

/**
 * 格式化DSL组件信息用于显示
 * @param {Object} components - DSL组件配置
 * @returns {Array} 格式化的组件信息
 */
export function formatComponentsInfo(components) {
  return Object.entries(components).map(([nodeId, component]) => {
    const componentName = component.obj.component_name;
    const inputs = component.obj.inputs || [];
    const output = component.obj.output;
    
    return {
      nodeId,
      componentName,
      hasInputs: inputs.length > 0,
      hasOutputs: output && Object.keys(output).length > 0,
      hasConversationHistory: inputs.some(input => 
        input.content && (
          input.content.includes('USER:') || 
          input.content.includes('ASSISTANT:')
        )
      ),
      inputCount: inputs.length,
      upstream: component.upstream || [],
      downstream: component.downstream || []
    };
  });
}
