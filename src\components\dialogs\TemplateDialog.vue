<template>
  <el-dialog
    :visible.sync="dialogVisible"
    :title="'模板转换 节点设置'"
    width="550px"
    :before-close="handleClose"
    custom-class="template-dialog"
  >
    <div class="dialog-content">
      <p class="dialog-description">
        将内容转换为预定义的模板格式，标准化输出结构。
      </p>
      
      <div class="description-box">
        <p>该组件用于排版各种组件的输出。1、支持Jinja2模板，会先将输入转为对象后进行模板渲染2、同时保留原使用(参数)字符串替换的方式</p>
      </div>

      <div class="section-title">基本设置</div>
      
      <el-form label-position="top" label-width="100px" size="small">
        <el-form-item label="节点名称">
          <el-input v-model="formData.title" placeholder="请输入节点名称"></el-input>
        </el-form-item>
        
        <el-form-item label="描述">
          <el-input type="textarea" v-model="formData.description" rows="3" placeholder="请输入描述"></el-input>
        </el-form-item>
      </el-form>

      <div class="section-title">内容</div>
      <el-input
        type="textarea"
        v-model="formData.content"
        :rows="8"
        placeholder="请输入"
      ></el-input>
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSave">保存设置</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'TemplateDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    templateData: {
      type: Object,
      default: () => ({
        id: '',
        title: '',
        description: '',
        content: '',
        onSave: null
      })
    }
  },
  data() {
    return {
      formData: {
        title: '',
        description: '',
        content: ''
      }
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        if (!val) {
          this.$emit('close');
        }
      }
    }
  },
  watch: {
    templateData: {
      handler(newVal) {
        this.formData = {
          title: newVal.title || '',
          description: newVal.description || '',
          content: newVal.content || ''
        };
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    handleClose() {
      this.$emit('close');
    },
    handleSave() {
      if (typeof this.templateData.onSave === 'function') {
        this.templateData.onSave(
          this.formData.title,
          this.formData.description,
          this.formData.content
        );
      }
      this.$emit('close');
    }
  }
};
</script>

<style scoped>
.template-dialog {
  border-radius: 4px;
}

.dialog-content {
  padding: 0 20px;
}

.dialog-description {
  margin-top: 0;
  margin-bottom: 20px;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.description-box {
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 12px 16px;
  margin-bottom: 20px;
}

.description-box p {
  margin: 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin: 20px 0 16px;
}

.el-form-item {
  margin-bottom: 20px;
}
</style> 