<template>
  <el-dialog
    :visible.sync="dialogVisible"
    :title="dialogTitle"
    width="500px"
    :close-on-click-modal="false"
  >
    <div class="loop-edit-form">
      <div class="node-description">
        重复执行特定操作，直到满足结束条件，用于批量处理或迭代改进。
      </div>
      <el-form label-position="top">
        <el-form-item label="标题">
          <el-input v-model="currentLoop.title" placeholder="输入循环标题"></el-input>
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            type="textarea"
            v-model="currentLoop.content"
            :rows="6"
            placeholder="输入循环描述..."
          ></el-input>
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSave">保存</el-button>
    </span>
  </el-dialog>
</template>

<script>
/**
 * 循环对话框组件
 * 
 * 重复执行特定操作，直到满足结束条件，用于批量处理或迭代改进。
 */
export default {
  name: 'LoopDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    loopData: {
      type: Object,
      default: () => ({
        id: '',
        title: '',
        content: '',
        onSave: null
      })
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(value) {
        if (!value) {
          this.$emit('close');
        }
      }
    },
    dialogTitle() {
      return this.currentLoop.id ? '编辑循环' : '添加循环';
    }
  },
  data() {
    return {
      currentLoop: {
        id: '',
        title: '',
        content: '',
        onSave: null
      }
    };
  },
  watch: {
    loopData: {
      handler(newVal) {
        this.currentLoop = { ...newVal };
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    handleSave() {
      console.log('Saving loop:', this.currentLoop.title, this.currentLoop.content);
      if (this.currentLoop.onSave) {
        this.currentLoop.onSave(this.currentLoop.title, this.currentLoop.content);
      }
      this.$emit('close');
    },
    handleCancel() {
      this.$emit('close');
    }
  }
}
</script>

<style scoped>
.loop-edit-form {
  padding: 0 20px;
}

.node-description {
  background-color: #E8F7FF;
  color: #666;
  padding: 10px 15px;
  border-radius: 4px;
  margin-bottom: 15px;
  border-left: 3px solid #1890FF;
  font-size: 14px;
  line-height: 1.5;
}
</style> 