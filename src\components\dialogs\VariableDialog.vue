<template>
  <el-dialog
    :visible.sync="dialogVisible"
    :title="dialogTitle"
    width="500px"
    :close-on-click-modal="false"
  >
    <div class="variable-edit-form">
      <div class="node-description">
        变量用于在工作流的不同节点之间共享和传递数据，支持多种数据类型。
      </div>
      <el-form :model="variableForm" label-width="80px">
        <el-form-item label="变量名称" required>
          <el-input v-model="variableForm.name" placeholder="请输入变量名称"></el-input>
        </el-form-item>
        <el-form-item label="变量类型">
          <el-select v-model="variableForm.type" placeholder="请选择变量类型">
            <el-option label="字符串" value="string"></el-option>
            <el-option label="数字" value="number"></el-option>
            <el-option label="布尔值" value="boolean"></el-option>
            <el-option label="数组" value="array"></el-option>
            <el-option label="对象" value="object"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="默认值">
          <el-input 
            v-model="variableForm.value" 
            :placeholder="getValuePlaceholder()"
            :type="variableForm.type === 'number' ? 'number' : 'text'"
          ></el-input>
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleAdd">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
/**
 * 变量编辑对话框组件
 * 
 * 该组件用于创建或编辑工作流中使用的变量。
 * 允许用户设置变量的名称、类型和初始值。
 * 支持多种变量类型，如字符串、数字、布尔值等。
 * 变量可以在工作流的不同节点之间共享和传递数据。
 * 通过props接收初始数据，并通过事件发送编辑结果。
 */
export default {
  name: 'VariableDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    editMode: {
      type: Boolean,
      default: false
    },
    initialData: {
      type: Object,
      default: () => ({
        name: '',
        type: 'string',
        value: ''
      })
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(value) {
        if (!value) {
          this.$emit('close');
        }
      }
    },
    dialogTitle() {
      return this.editMode ? '编辑变量' : '添加变量';
    }
  },
  data() {
    return {
      variableForm: {
        name: '',
        type: 'string',
        value: ''
      }
    };
  },
  watch: {
    visible(val) {
      if (val) {
        // 重置表单
        this.variableForm = {
          name: '',
          source: 'DeepSeek'
        };
      }
    }
  },
  methods: {
    handleCancel() {
      this.$emit('close');
    },
    handleAdd() {
      if (!this.variableForm.name) {
        this.$message.error('请输入变量名称');
        return;
      }
      
      // 发送添加事件
      this.$emit('add', {
        name: this.variableForm.name,
        type: this.variableForm.type,
        value: this.variableForm.value
      });
      
      // 关闭对话框
      this.$emit('close');
    },
    getValuePlaceholder() {
      const placeholders = {
        'string': '请输入字符串值',
        'number': '请输入数字值',
        'boolean': '请输入true或false',
        'array': '请输入JSON数组，如[1,2,3]',
        'object': '请输入JSON对象，如{"key":"value"}'
      };
      return placeholders[this.variableForm.type] || '请输入默认值';
    }
  }
}
</script>

<style scoped>
.variable-edit-form {
  padding: 0 20px;
}

.node-description {
  background-color: #EFE8FF;
  color: #666;
  padding: 10px 15px;
  border-radius: 4px;
  margin-bottom: 15px;
  border-left: 3px solid #722ED1;
  font-size: 14px;
  line-height: 1.5;
}
</style> 