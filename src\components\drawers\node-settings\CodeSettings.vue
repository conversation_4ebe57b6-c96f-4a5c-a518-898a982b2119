<template>
  <div class="code-settings">
    <div class="settings-section">
      <div class="section-header">
        <h3 class="section-title">输入变量</h3>
        <el-button type="primary" icon="el-icon-plus" @click="addVar" size="small" plain>新增变量</el-button>
      </div>
      <div class="vars-list">
        <div v-for="(variable, idx) in variables" :key="idx" class="var-row">
          <el-input v-model="variable.name" placeholder="变量名" class="var-input" size="small" />
          <el-select v-model="variable.type" placeholder="类型" class="var-type" size="small" style="width: 120px">
            <el-option v-for="name in modelNodeNames" :key="name" :label="name" :value="name" />
          </el-select>
          <el-button icon="el-icon-delete" @click="removeVar(idx)" type="text" size="small" class="var-del-btn" />
        </div>
      </div>
    </div>
    <div class="settings-section">
      <div class="section-header">
        <h3 class="section-title">代码编辑</h3>
        <div class="editor-toolbar">
          <el-select v-model="language" size="mini" class="lang-select" @change="onLangChange">
            <el-option label="Python" value="python" />
            <el-option label="JavaScript" value="javascript" />
          </el-select>
          <el-switch v-model="isDark" active-text="深色" inactive-text="浅色" class="theme-switch" />
        </div>
      </div>
      <div :class="['code-editor-card', isDark ? 'theme-dark' : 'theme-light']">
        <prism-editor
          v-model="code"
          :highlight="highlighter"
          line-numbers
          class="code-editor"
          :language="language"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { PrismEditor } from 'vue-prism-editor';
import 'vue-prism-editor/dist/prismeditor.min.css';
import 'prismjs';
import 'prismjs/components/prism-python';
import 'prismjs/components/prism-javascript';

const DEFAULT_CODE = {
  python: 'def main(arg1: str, arg2: str) -> str:\n    return f"result: {arg1 + arg2}"',
  javascript: 'function main(arg1, arg2) {\n  return `result: ${arg1 + arg2}`;\n}'
};

export default {
  name: 'CodeSettings',
  components: { PrismEditor },
  props: {
    value: Object, // v-model: { variables: [], code: '' }
  },
  data() {
    return {
      variables: this.value?.variables ? JSON.parse(JSON.stringify(this.value.variables)) : [
        { name: 'arg1', type: 'str' },
        { name: 'arg2', type: 'str' }
      ],
      code: this.value?.code || DEFAULT_CODE.python,
      language: 'python',
      isDark: true,
      modelNodeNames: [
        '知识检索',
        '生成回答',
        '对话',
        '问题分类',
        '静态消息',
        '问题优化',
        '关键词',
        '条件',
        '集线器',
        '模板转换',
        '循环',
        '代码',
        '注释'
      ]
    };
  },
  watch: {
    variables: {
      handler() {
        this.emitChange();
      },
      deep: true
    },
    code() {
      this.emitChange();
    },
    language(newLang, oldLang) {
      // 切换语言时，如果当前代码是默认模板，则切换为新语言的默认模板
      if (this.code === DEFAULT_CODE[oldLang]) {
        this.code = DEFAULT_CODE[newLang];
      }
    }
  },
  methods: {
    addVar() {
      this.variables.push({ name: '', type: 'str' });
    },
    removeVar(idx) {
      this.variables.splice(idx, 1);
    },
    emitChange() {
      this.$emit('input', { variables: this.variables, code: this.code, language: this.language, theme: this.isDark ? 'dark' : 'light' });
    },
    highlighter(code) {
      // prismjs 已自动高亮
      return code;
    },
    onLangChange() {
      // 切换语言时，如果当前代码是默认模板，则切换为新语言的默认模板
      if (Object.values(DEFAULT_CODE).includes(this.code)) {
        this.code = DEFAULT_CODE[this.language];
      }
    }
  }
};
</script>

<style scoped>
@import '@/assets/prism-themes/prism-vsc-dark-plus.css';

.code-settings {
  padding: 10px 0;
}
.settings-section {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 8px;
}
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.section-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}
.editor-toolbar {
  display: flex;
  align-items: center;
  gap: 12px;
}
.lang-select {
  width: 110px;
}
.theme-switch {
  margin-left: 8px;
}
.code-editor-card {
  border-radius: 6px;
  border: 1px solid #232323;
  box-shadow: 0 2px 8px rgba(0,0,0,0.12);
  padding: 0;
  min-height: 140px;
  margin-top: 0;
  overflow: auto;
  transition: background 0.2s;
}
.code-editor-card.theme-dark {
  background: #1e1e1e;
}
.code-editor-card.theme-light {
  background: #f5f5f5;
  border: 1px solid #e0e0e0;
}
.code-editor {
  font-size: 15px;
  min-height: 140px;
  background: transparent;
  border: none;
  border-radius: 6px;
  box-shadow: none;
  padding: 14px 18px;
  line-height: 1.7;
  color: #d4d4d4;
  font-family: 'Fira Mono', 'Consolas', 'Menlo', 'Monaco', 'monospace';
  caret-color: #fff;
  transition: color 0.2s, background 0.2s;
}
.code-editor-card.theme-light .code-editor {
  color: #232323;
  caret-color: #232323;
}
/* Prism Editor VSCode风格增强 */
.code-editor ::v-deep .prism-editor__container {
  background: transparent !important;
}
.code-editor ::v-deep .prism-editor__textarea {
  color: inherit !important;
  background: transparent !important;
  font-family: 'Fira Mono', 'Consolas', 'Menlo', 'Monaco', 'monospace';
}
.code-editor ::v-deep .prism-editor__line-numbers {
  background: #232323 !important;
  color: #858585 !important;
  border-right: 1px solid #232323;
}
.code-editor-card.theme-light .code-editor ::v-deep .prism-editor__line-numbers {
  background: #e0e0e0 !important;
  color: #b0b0b0 !important;
  border-right: 1px solid #e0e0e0;
}
.code-editor ::v-deep .prism-editor__line-number {
  color: #858585 !important;
}
.code-editor-card.theme-light .code-editor ::v-deep .prism-editor__line-number {
  color: #b0b0b0 !important;
}
.code-editor ::v-deep pre {
  background: transparent !important;
  color: inherit !important;
}
/* 只保留深色 VSCode 主题 token 样式 */
.code-editor.theme-dark ::v-deep .token.comment,
.code-editor.theme-dark ::v-deep .token.prolog,
.code-editor.theme-dark ::v-deep .token.doctype,
.code-editor.theme-dark ::v-deep .token.cdata {
  color: #6a9955 !important;
}
.code-editor.theme-dark ::v-deep .token.keyword {
  color: #569cd6 !important;
}
.code-editor.theme-dark ::v-deep .token.string {
  color: #ce9178 !important;
}
.code-editor.theme-dark ::v-deep .token.function {
  color: #dcdcaa !important;
}
.code-editor.theme-dark ::v-deep .token.number {
  color: #b5cea8 !important;
}
.code-editor.theme-dark ::v-deep .token.operator {
  color: #d4d4d4 !important;
}
.code-editor.theme-dark ::v-deep .token.class-name {
  color: #4ec9b0 !important;
}
.code-editor.theme-dark ::v-deep .token.boolean {
  color: #569cd6 !important;
}
.code-editor.theme-dark ::v-deep .token.punctuation {
  color: #d4d4d4 !important;
}
.code-editor ::v-deep .prism-editor__scroll-container {
  scrollbar-color: #232323 #1e1e1e;
  scrollbar-width: thin;
}
.code-editor-card.theme-light .code-editor ::v-deep .prism-editor__scroll-container {
  scrollbar-color: #e0e0e0 #f5f5f5;
}
.code-editor ::v-deep .prism-editor__scroll-container::-webkit-scrollbar {
  width: 8px;
  background: #232323;
}
.code-editor-card.theme-light .code-editor ::v-deep .prism-editor__scroll-container::-webkit-scrollbar {
  background: #e0e0e0;
}
.code-editor ::v-deep .prism-editor__scroll-container::-webkit-scrollbar-thumb {
  background: #444;
  border-radius: 4px;
}
.code-editor-card.theme-light .code-editor ::v-deep .prism-editor__scroll-container::-webkit-scrollbar-thumb {
  background: #b0b0b0;
}
</style> 