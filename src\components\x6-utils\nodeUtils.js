/**
 * 节点工具函数模块
 */

/**
 * 根据节点类型获取SVG图标路径
 * @param {String} type - 节点类型
 * @returns {String} SVG路径
 */
export function getSvgPathForType(type) {
  // 这里可以根据不同节点类型返回不同的SVG图标路径
  const paths = {
    'default': 'M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 3c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm0 14.2c-2.5 0-4.71-1.28-6-3.22.03-1.99 4-3.08 6-3.08 1.99 0 5.97 1.09 6 3.08-1.29 1.94-3.5 3.22-6 3.22z',
    'optimization': 'M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z',
    'keywords': 'M21.41 11.58l-9-9C12.05 2.22 11.55 2 11 2H4c-1.1 0-2 .9-2 2v7c0 .55.22 1.05.59 1.42l9 9c.36.36.86.58 1.41.58s1.05-.22 1.41-.59l7-7c.37-.36.59-.86.59-1.41s-.23-1.06-.59-1.42zM5.5 7C4.67 7 4 6.33 4 5.5S4.67 4 5.5 4 7 4.67 7 5.5 6.33 7 5.5 7z',
    'generation': 'M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.95-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z',
    'retrieval': 'M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z',
    'dialogue': 'M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z',
    'classification': 'M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 1.99 2H18c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z',
    'message': 'M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 14H6v-2h12v2zm0-3H6v-2h12v2zm0-3H6V8h12v2z',
    'conditions': 'M14 6l-4.22 5.63 1.25 1.67L14 9.33 19 16h-8.46l-4.01-5.37L1 18h22L14 6zM5 16l1.52-2.03L8.04 16H5z',
    'hub': 'M20 13H4c-.55 0-1 .45-1 1v6c0 .55.45 1 1 1h16c.55 0 1-.45 1-1v-6c0-.55-.45-1-1-1zm-1 6H5v-4h14v4zM20 3H4c-.55 0-1 .45-1 1v6c0 .55.45 1 1 1h16c.55 0 1-.45 1-1V4c0-.55-.45-1-1-1zm-1 6H5V5h14v4z',
    'template': 'M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z',
    'loop': 'M12 4V1L8 5l4 4V6c3.31 0 6 2.69 6 6 0 1.01-.25 1.97-.7 2.8l1.46 1.46C19.54 15.03 20 13.57 20 12c0-4.42-3.58-8-8-8zm0 14c-3.31 0-6-2.69-6-6 0-1.01.25-1.97.7-2.8L5.24 7.74C4.46 8.97 4 10.43 4 12c0 4.42 3.58 8 8 8v3l4-4-4-4v3z',
    'code': 'M9.4 16.6L4.8 12l4.6-4.6L8 6l-6 6 6 6 1.4-1.4zm5.2 0l4.6-4.6-4.6-4.6L16 6l6 6-6 6-1.4-1.4z',
    'comment': 'M14 17H4v2h10v-2zm6-8H4v2h16V9zM4 15h16v-2H4v2zM4 5v2h16V5H4z'
  };
  
  return paths[type] || paths.default;
}

/**
 * 选择节点
 * @param {Object} graph - X6图表实例
 * @param {Object} node - 要选择的节点
 */
export function selectNode(graph, node) {
  if (!graph || !node) return;
  
  try {
    // 先清除所有节点的选择状态
    clearSelection(graph);
    
    // 添加自定义高亮效果 - 只设置实线蓝色边框
    node.attr('body', {
      stroke: '#1890FF',
      strokeWidth: 2,
    });
    
    // 如果是循环节点，同时高亮其内容区域
    const nodeData = node.getData();
    if (nodeData && nodeData.isLoop) {
      const contentId = `${node.id}-content`;
      const contentNode = graph.getCellById(contentId);
      if (contentNode) {
        contentNode.attr('body', {
          stroke: '#1890FF',
          strokeWidth: 2,
          strokeDasharray: 'none', // 使用实线而不是虚线
        });
      }
    }
    
    // 添加节点到选择集合但不显示选择框
    if (typeof graph.select === 'function') {
      // 暂存原来的showNodeSelectionBox值
      const originalShowBox = graph.options.selecting.showNodeSelectionBox;
      // 临时设置为false
      graph.options.selecting.showNodeSelectionBox = false;
      // 添加节点到选择
      graph.select().addNode(node);
      // 恢复原来的设置
      graph.options.selecting.showNodeSelectionBox = originalShowBox;
    }
  } catch (err) {
    console.error('Error in selectNode helper:', err);
  }
}

/**
 * 清除节点选择
 * @param {Object} graph - X6图表实例
 */
export function clearSelection(graph) {
  if (!graph) return;
  
  try {
    // 清除所有节点的自定义边框样式
    const nodes = graph.getNodes();
    nodes.forEach(node => {
      node.attr('body', {
        stroke: 'transparent',
        strokeWidth: 0,
      });
      
      // 如果是循环节点，恢复其内容区域的样式
      const nodeData = node.getData();
      if (nodeData && nodeData.isLoop) {
        const contentId = `${node.id}-content`;
        const contentNode = graph.getCellById(contentId);
        if (contentNode) {
          contentNode.attr('body', {
            stroke: '#1890FF',
            strokeWidth: 1,
            strokeDasharray: '5 5', // 恢复为虚线
          });
        }
      }
    });
    
    // 使用X6的API清除选择
    if (typeof graph.cleanSelection === 'function') {
      graph.cleanSelection();
    } else if (typeof graph.select === 'function') {
      graph.select().clean();
    }
  } catch (err) {
    console.error('Error in clearSelection:', err);
  }
}

/**
 * 检查是否点击了调整大小的手柄
 * @param {Event} e - 鼠标事件
 * @param {Object} node - 节点对象
 * @param {Object} graph - 图表实例
 * @returns {Boolean} 是否点击了调整大小的手柄
 */
export function isResizeHandleClicked(e, node, graph) {
  const { x, y } = graph.clientToLocal(e.clientX, e.clientY);
  const bbox = node.getBBox();
  const handleSize = 24; // 增大手柄的可点击区域
  
  // 检查点击是否在右下角的手柄区域
  return (
    x >= bbox.x + bbox.width - handleSize &&
    x <= bbox.x + bbox.width &&
    y >= bbox.y + bbox.height - handleSize &&
    y <= bbox.y + bbox.height
  );
} 

/**
 * 将 DSL 的 category_description 对象转为 categories 数组
 * @param {Object} categoryDescription - 形如 { "娃哈哈": {...}, "嘻嘻嘻": {...} }
 * @returns {Array} categories - 形如 [ { name: "娃哈哈", ... }, { name: "嘻嘻嘻", ... } ]
 */
export function parseCategoriesFromDSL(categoryDescription) {
  if (!categoryDescription || typeof categoryDescription !== 'object') return [];
  return Object.keys(categoryDescription).map(key => ({
    name: key,
    ...(categoryDescription[key] || {})
  }));
} 

/**
 * 生成 handle → 端口id 的映射表
 * @param {Array} items - 端口分支数组（如 categories、cases）
 * @param {String} prefix - 端口id前缀（如 'category-port-', 'case-port-')
 * @param {String} key - handle 匹配的字段名（如 'name', 'label', 'value')
 * @returns {Object} handle → 端口id 映射表
 */
export function buildPortHandleMap(items, prefix = 'category-port-', key = 'name') {
  const map = {};
  if (Array.isArray(items)) {
    items.forEach((item, idx) => {
      if (item && item[key] !== undefined) {
        map[String(item[key]).trim()] = `${prefix}${idx}`;
      }
    });
  }
  return map;
} 