// CHUNK MANAGEMENT WITHIN DATASET API 封装
// 这里将封装分块的添加、检索、解析、停止解析等接口

// TODO: 实现相关API函数 
import ragflowRequest, { RAGFLOW_API_KEY } from './index';

/**
 * 向数据集文档添加chunk
 * @param {string} datasetId - 数据集ID
 * @param {string} documentId - 文档ID
 * @param {Object} data - { content, important_keywords?, questions? }
 * @returns {Promise}
 */
export function addChunkToDocument(datasetId, documentId, data) {
  return ragflowRequest.post(
    `/api/v1/datasets/${datasetId}/documents/${documentId}/chunks`,
    data,
    {
      headers: {
        'Authorization': `Bearer ${RAGFLOW_API_KEY}`,
        'Content-Type': 'application/json'
      }
    }
  );
}

/**
 * 获取文档内chunk列表
 * @param {string} datasetId - 数据集ID
 * @param {string} documentId - 文档ID
 * @param {Object} params - 查询参数，如 { keywords, page, page_size, id }
 * @returns {Promise}
 */
export function listDocumentChunks(datasetId, documentId, params = {}) {
  return ragflowRequest.get(
    `/api/v1/datasets/${datasetId}/documents/${documentId}/chunks`,
    {
      params,
      headers: {
        'Authorization': `Bearer ${RAGFLOW_API_KEY}`,
        'Content-Type': 'application/json'
      }
    }
  );
} 

/**
 * 删除文档内chunk
 * @param {string} datasetId - 数据集ID
 * @param {string} documentId - 文档ID
 * @param {Array<string>} chunkIds - 要删除的chunk ID列表（可选，空则删除全部）
 * @returns {Promise}
 */
export function deleteDocumentChunks(datasetId, documentId, chunkIds = []) {
  return ragflowRequest.delete(
    `/api/v1/datasets/${datasetId}/documents/${documentId}/chunks`,
    {
      data: { chunk_ids: chunkIds },
      headers: {
        'Authorization': `Bearer ${RAGFLOW_API_KEY}`,
        'Content-Type': 'application/json'
      }
    }
  );
} 

/**
 * 更新chunk内容或配置
 * @param {string} datasetId - 数据集ID
 * @param {string} documentId - 文档ID
 * @param {string} chunkId - chunk ID
 * @param {Object} data - 要更新的字段，如 { content, important_keywords, available }
 * @returns {Promise}
 */
export function updateChunk(datasetId, documentId, chunkId, data) {
  return ragflowRequest.put(
    `/api/v1/datasets/${datasetId}/documents/${documentId}/chunks/${chunkId}`,
    data,
    {
      headers: {
        'Authorization': `Bearer ${RAGFLOW_API_KEY}`,
        'Content-Type': 'application/json'
      }
    }
  );
} 

/**
 * 检索数据集/文档chunk
 * @param {Object} params - 检索参数，如 { question, dataset_ids, document_ids, ... }
 * @returns {Promise}
 */
export function retrieveChunks(params) {
  // 数据完整性验证
  const validatedParams = validateRetrievalParams(params);

  return ragflowRequest.post(
    '/api/v1/retrieval',
    validatedParams,
    {
      headers: {
        'Authorization': `Bearer ${RAGFLOW_API_KEY}`,
        'Content-Type': 'application/json'
      }
    }
  );
}

/**
 * 验证检索参数的完整性
 * @param {Object} params - 原始参数
 * @returns {Object} - 验证后的参数
 */
function validateRetrievalParams(params) {
  let validated = { ...params };

  // 必需参数验证
  if (!validated.question || typeof validated.question !== 'string') {
    throw new Error('检索参数错误：question字段必须是非空字符串');
  }

  if (!validated.dataset_ids || !Array.isArray(validated.dataset_ids) || validated.dataset_ids.length === 0) {
    throw new Error('检索参数错误：dataset_ids字段必须是非空数组');
  }

  // 参数默认值设置
  validated.similarity_threshold = validated.similarity_threshold ?? 0.5;
  validated.vector_similarity_weight = validated.vector_similarity_weight ?? 0.5;
  validated.top_k = validated.top_k ?? 3;
  validated.keyword = validated.keyword ?? true;
  validated.highlight = validated.highlight ?? true;

  // 参数范围验证
  if (validated.similarity_threshold < 0 || validated.similarity_threshold > 1) {
    console.warn('相似度阈值超出范围[0,1]，已重置为0.5');
    validated.similarity_threshold = 0.5;
  }

  if (validated.vector_similarity_weight < 0 || validated.vector_similarity_weight > 1) {
    console.warn('向量相似度权重超出范围[0,1]，已重置为0.5');
    validated.vector_similarity_weight = 0.5;
  }

  if (validated.top_k < 1 || validated.top_k > 100) {
    console.warn('检索数量超出范围[1,100]，已重置为3');
    validated.top_k = 3;
  }

  console.log('[检索参数验证] 参数验证通过:', validated);
  return validated;
}