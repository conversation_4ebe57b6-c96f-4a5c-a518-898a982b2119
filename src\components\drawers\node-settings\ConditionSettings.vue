<template>
  <div class="condition-settings">
    <!-- 分支与ELSE信息总览已移除 -->
    <div class="settings-section">
      <div class="section-header">
        <h3 class="section-title">基本设置</h3>
      </div>
      
      <el-form label-position="top">
        <el-form-item label="节点标题">
          <el-input 
            v-model="title" 
            placeholder="请输入节点标题"
            size="small"
            @change="updateNodeData"
          ></el-input>
        </el-form-item>
      </el-form>
    </div>

    <div class="settings-section">
      <div class="section-header">
        <h3 class="section-title">条件分支</h3>
        <el-button 
          type="primary" 
          size="small" 
          @click="addCase"
          icon="el-icon-plus"
        >添加分支</el-button>
      </div>
      
      <div v-if="cases.length === 0" class="empty-categories">
        <p>暂无条件分支，请点击"添加分支"按钮添加</p>
      </div>
      
      <div v-else class="cases-container">
        <div 
          v-for="(caseItem, caseIndex) in cases" 
          :key="'case-'+caseIndex"
          class="case-item"
        >
          <div class="case-header">
            <el-tag class="case-tag" effect="plain" :type="getCaseTagType(caseIndex)">
              Case {{ caseIndex + 1 }}
            </el-tag>
            <i class="el-icon-delete" @click="removeCase(caseIndex)"></i>
          </div>
          <div class="case-content">
            <div class="case-step-header">
              <span>下一步</span>
            </div>
            <el-input 
              v-model="caseItem.to" 
              placeholder="请输入下一步节点ID"
              size="small"
              style="width: 100%; margin-bottom: 15px;"
              @change="updateNodeData"
            />
            <div class="case-condition-header">
              <span>条件</span>
            </div>
            <!-- 条件列表 -->
            <div class="conditions-list">
              <div 
                v-for="(condition, condIndex) in caseItem.conditions" 
                :key="'cond-'+caseIndex+'-'+condIndex"
                class="condition-item"
              >
                <!-- 条件操作符连接（从第二个条件开始显示） -->
                <div v-if="condIndex > 0" class="condition-operator">
                  <el-select 
                    v-model="condition.operator" 
                    size="mini"
                    style="width: 80px;"
                    @change="updateNodeData"
                  >
                    <el-option label="与" value="and"></el-option>
                    <el-option label="或" value="or"></el-option>
                  </el-select>
                </div>
                <!-- 条件表达式 -->
                <div class="condition-expression">
                  <el-input 
                    v-model="condition.cpn_id" 
                    placeholder="组件ID"
                    size="small"
                    style="width: 120px; margin-right: 5px;"
                    @change="updateNodeData"
                  />
                  <el-select 
                    v-model="condition.comparator" 
                    placeholder="操作符"
                    size="small"
                    style="width: 100px; margin-right: 5px;"
                    @change="updateNodeData"
                  >
                    <el-option label="等于" value="equals"></el-option>
                    <el-option label="不等于" value="notEquals"></el-option>
                    <el-option label="大于" value="greaterThan"></el-option>
                    <el-option label="小于" value="lessThan"></el-option>
                    <el-option label="包含" value="contains"></el-option>
                    <el-option label="不包含" value="notContains"></el-option>
                  </el-select>
                  <el-input 
                    v-model="condition.value" 
                    placeholder="值"
                    size="small"
                    style="width: calc(100% - 230px);"
                    @change="updateNodeData"
                  />
                  <i 
                    class="el-icon-delete condition-delete" 
                    @click="removeCondition(caseIndex, condIndex)"
                  ></i>
                </div>
              </div>
              <!-- 添加条件按钮 -->
              <el-button 
                type="text" 
                icon="el-icon-plus" 
                class="add-condition-button"
                @click="addCondition(caseIndex)"
              >
                添加条件
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="settings-section">
      <div class="section-header">
        <h3 class="section-title">ELSE分支</h3>
        <el-tag effect="plain" type="warning">默认路径</el-tag>
      </div>
      
      <el-form label-position="top">
        <el-form-item label="执行操作">
          <el-select 
            v-model="elseAction" 
            placeholder="请选择操作"
            size="small"
            style="width: 100%"
            @change="updateNodeData"
          >
            <el-option label="知识检索" value="retrieval"></el-option>
            <el-option label="生成回答" value="generation"></el-option>
            <el-option label="对话" value="dialogue"></el-option>
            <el-option label="静态消息" value="message"></el-option>
            <el-option label="问题优化" value="optimization"></el-option>
            <el-option label="关键词" value="keywords"></el-option>
            <el-option label="条件" value="conditions"></el-option>
            <el-option label="集线器" value="hub"></el-option>
            <el-option label="模板转换" value="template"></el-option>
            <el-option label="循环" value="loop"></el-option>
            <el-option label="代码" value="code"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    
    <div class="settings-actions">
      <el-button type="primary" @click="saveSettings">保存设置</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ConditionSettings',
  props: {
    nodeData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      title: '',
      cases: [],
      elseAction: ''
    }
  },
  computed: {
    elseTarget() {
      // 优先取 nodeData.form.end_cpn_id，其次 elseAction
      return this.nodeData?.form?.end_cpn_id || this.elseAction || '无';
    }
  },
  watch: {
    nodeData: {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.title = newVal.title || '条件';
          // 优先用 form.conditions
          if (newVal.form && Array.isArray(newVal.form.conditions) && newVal.form.conditions.length > 0) {
            this.cases = newVal.form.conditions.map(item => ({
              ...item,
              conditions: Array.isArray(item.items) ? item.items : (item.conditions || [])
            }));
            this.elseAction = newVal.form.end_cpn_id || '';
          } else if (Array.isArray(newVal.cases) && newVal.cases.length > 0) {
            this.cases = JSON.parse(JSON.stringify(newVal.cases)).map(item => ({
              ...item,
              conditions: Array.isArray(item.conditions) ? item.conditions : [this.createDefaultCondition()]
            }));
            this.elseAction = newVal.elseAction || '';
          } else {
            this.cases = [];
            this.elseAction = '';
          }
        }
      }
    }
  },
  methods: {
    // 根据Case索引获取标签类型
    getCaseTagType(index) {
      const types = ['primary', 'success', 'warning', 'info'];
      return types[index % types.length];
    },
    
    // 创建默认条件
    createDefaultCondition() {
      return {
        component: '',
        comparator: 'equals',
        value: '',
        operator: 'and'
      };
    },
    
    // 添加分支
    addCase() {
      this.cases.push({
        action: '',
        conditions: [this.createDefaultCondition()]
      });
      this.updateNodeData();
    },
    
    // 移除分支
    removeCase(index) {
      this.cases.splice(index, 1);
      this.updateNodeData();
    },
    
    // 添加条件
    addCondition(caseIndex) {
      if (this.cases[caseIndex]) {
        if (!Array.isArray(this.cases[caseIndex].conditions)) {
          this.cases[caseIndex].conditions = [];
        }
        this.cases[caseIndex].conditions.push(this.createDefaultCondition());
        this.updateNodeData();
      }
    },
    
    // 移除条件
    removeCondition(caseIndex, condIndex) {
      if (this.cases[caseIndex] && 
          Array.isArray(this.cases[caseIndex].conditions) && 
          this.cases[caseIndex].conditions.length > condIndex) {
        
        // 如果只有一个条件，不允许删除
        if (this.cases[caseIndex].conditions.length === 1) {
          this.$message.warning('每个分支至少需要一个条件');
          return;
        }
        
        this.cases[caseIndex].conditions.splice(condIndex, 1);
        this.updateNodeData();
      }
    },
    
    // 更新节点数据
    updateNodeData() {
      this.$emit('update:nodeData', {
        ...this.nodeData,
        title: this.title,
        cases: JSON.parse(JSON.stringify(this.cases)),
        elseAction: this.elseAction
      });
    },
    
    // 保存设置
    saveSettings() {
      // 使用非响应式版本的 cases 数组
      const nonReactiveCases = JSON.parse(JSON.stringify(this.cases));

      // 转换为正确的form格式
      const conditions = nonReactiveCases.map(caseItem => ({
        items: caseItem.conditions || [],
        logical_operator: caseItem.logical_operator || 'and',
        to: caseItem.action || ''
      }));

      // 构建完整的保存数据
      const saveData = {
        title: this.title,
        label: 'Switch',  // 确保label是英文
        cases: nonReactiveCases,
        elseAction: this.elseAction,
        form: {
          ...this.nodeData.form,
          conditions: conditions,
          end_cpn_id: this.elseAction
        }
      };

      // 发出事件，完全替换旧数据
      this.$emit('update:nodeData', saveData);

      this.$message({
        message: '设置已保存',
        type: 'success',
        duration: 2000
      });
    },

    // 只读展示区用：格式化条件
    formatCondition(cond) {
      if (!cond) return '';
      // 兼容多种结构
      const comp = cond.component || cond.cpn_id || '';
      const op = cond.comparator || cond.operator || '=';
      const val = cond.value || '';
      return `${comp} ${op} ${val}`;
    },
    getCaseLogicalOperator(caseItem) {
      return caseItem.logical_operator || (caseItem.conditions && caseItem.conditions[0]?.operator) || 'and';
    },
    getCaseTo(caseItem) {
      return caseItem.to || caseItem.action || '未设置';
    }
  }
}
</script>

<style scoped>
.condition-settings {
  padding: 10px 0;
}

.settings-section {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.section-title {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.empty-categories {
  padding: 20px 0;
  text-align: center;
  color: #909399;
  font-size: 14px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.cases-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 500px;
  overflow-y: auto;
}

.case-item {
  padding: 12px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #ebeef5;
  margin-bottom: 10px;
}

.case-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.case-tag {
  font-weight: bold;
  font-size: 13px;
  padding: 4px 8px;
  border-width: 1px;
}

.case-header i {
  cursor: pointer;
  color: #F56C6C;
  font-size: 16px;
}

.case-header i:hover {
  color: #f78989;
}

.case-content {
  margin-bottom: 5px;
}

.case-step-header,
.case-condition-header {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
  margin-bottom: 8px;
}

.conditions-list {
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.condition-item {
  margin-bottom: 10px;
}

.condition-operator {
  margin-bottom: 8px;
}

.condition-expression {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.condition-delete {
  margin-left: 8px;
  color: #F56C6C;
  cursor: pointer;
  font-size: 16px;
}

.condition-delete:hover {
  color: #f78989;
}

.add-condition-button {
  margin-top: 5px;
  color: #8F2CFF;
}

.settings-actions {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style> 