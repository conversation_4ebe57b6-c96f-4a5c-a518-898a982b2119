<template>
  <div class="agent-management">
    <div class="page-header">
      <h2>Agent管理</h2>
      <el-button type="primary" icon="el-icon-plus" @click="showCreateDialog">
        创建Agent
      </el-button>
    </div>

    <!-- Agent卡片列表 -->
    <div class="agent-cards" v-loading="loading">
      <div v-if="agents.length === 0 && !loading" class="empty-state">
        <i class="el-icon-folder-opened"></i>
        <p>暂无Agent，点击"创建Agent"开始创建</p>
      </div>
      
      <div v-else class="cards-container">
        <div 
          v-for="agent in agents" 
          :key="agent.id" 
          class="agent-card"
          @click="viewAgent(agent)"
        >
          <div class="card-header">
            <div class="agent-title">
              <i class="el-icon-user"></i>
              <span>{{ agent.title }}</span>
            </div>
            <div class="card-actions">
              <el-button 
                size="mini" 
                type="text" 
                icon="el-icon-edit"
                @click.stop="editAgent(agent)"
                title="编辑"
              ></el-button>
              <el-button 
                size="mini" 
                type="text" 
                icon="el-icon-delete"
                @click.stop="deleteAgent(agent)"
                title="删除"
              ></el-button>
            </div>
          </div>
          
          <div class="card-content">
            <div class="agent-info">
              <div class="info-item">
                <i class="el-icon-time"></i>
                <span>创建时间：{{ formatDate(agent.create_date) }}</span>
              </div>
              <div class="info-item">
                <i class="el-icon-refresh"></i>
                <span>更新时间：{{ formatDate(agent.update_date) }}</span>
              </div>
            </div>
            
            <div class="agent-stats">
              <div class="stat-item">
                <span class="stat-label">节点数</span>
                <span class="stat-value">{{ getNodeCount(agent) }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">连接数</span>
                <span class="stat-value">{{ getEdgeCount(agent) }}</span>
              </div>
            </div>
          </div>
          
          <div class="card-footer">
            <el-tag size="small" type="primary">Agent</el-tag>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-container" v-if="agents.length > 0">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.page"
        :page-sizes="[12, 24, 48, 96]"
        :page-size="pagination.page_size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>



    <!-- Agent详情对话框 -->
    <el-dialog
      title="Agent详情"
      :visible.sync="detailVisible"
      width="70%"
    >
      <div v-if="selectedAgent" class="agent-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="ID">{{ selectedAgent.id }}</el-descriptions-item>
          <el-descriptions-item label="名称">{{ selectedAgent.title }}</el-descriptions-item>
          <el-descriptions-item label="描述">{{ selectedAgent.description || '无' }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDate(selectedAgent.create_date) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ formatDate(selectedAgent.update_date) }}</el-descriptions-item>
          <el-descriptions-item label="用户ID">{{ selectedAgent.user_id }}</el-descriptions-item>
        </el-descriptions>
        
        <div class="dsl-section">
          <h4>DSL配置</h4>
          <el-input
            type="textarea"
            v-model="dslDisplayText"
            :rows="15"
            readonly
          ></el-input>
        </div>
      </div>
    </el-dialog>

    <!-- 创建Agent对话框 -->
    <el-dialog
      title="创建Agent"
      :visible.sync="createDialogVisible"
      width="400px"
      @close="resetCreateForm"
    >
      <el-form :model="createForm" :rules="createRules" ref="createFormRef" label-width="80px">
        <el-form-item label="名称" prop="title">
          <el-input v-model="createForm.title" placeholder="请输入Agent名称" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="createDialogVisible = false">取 消</el-button>
        <el-button type="primary" :loading="createLoading" @click="handleCreateAgent">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listAgents, deleteAgent, createAgent } from '../api/agent-management-API';

export default {
  name: 'AgentManagement',
  data() {
    return {
      loading: false,
      agents: [],
      total: 0,
      pagination: {
        page: 1,
        page_size: 12 // 改为12，适合卡片布局
      },
      detailVisible: false,
      selectedAgent: null,
      dslDisplayText: '',
      createDialogVisible: false,
      createForm: {
        title: ''
      },
      createRules: {
        title: [
          { required: true, message: '请输入Agent名称', trigger: 'blur' }
        ]
      },
      createLoading: false
    };
  },

  mounted() {
    this.loadAgents();
  },
  methods: {
    // 加载Agent列表
    async loadAgents() {
      this.loading = true;
      try {
        const response = await listAgents({
          page: this.pagination.page,
          page_size: this.pagination.page_size,
          orderby: 'create_time',
          desc: true
        });
        
        if (response.data && response.data.data) {
          this.agents = response.data.data;
          this.total = response.data.total || this.agents.length;
        }
      } catch (error) {
        console.error('加载Agent列表失败:', error);
        this.$message.error('加载Agent列表失败');
      } finally {
        this.loading = false;
      }
    },

    // 显示创建对话框
    showCreateDialog() {
      this.createDialogVisible = true;
    },
    resetCreateForm() {
      this.createForm = {
        title: ''
      };
      if (this.$refs.createFormRef) this.$refs.createFormRef.resetFields();
    },
    async handleCreateAgent() {
      this.$refs.createFormRef.validate(async valid => {
        if (!valid) return;
        this.createLoading = true;
        try {
          await createAgent({
            title: this.createForm.title,
            dsl: {
              graph: {
                edges: [],
                nodes: [
                  {
                    data: { label: 'Begin', name: 'begin' },
                    id: 'begin',
                    position: { x: 50, y: 200 },
                    sourcePosition: 'left',
                    targetPosition: 'right',
                    type: 'beginNode'
                  }
                ]
              },
              components: {
                begin: {
                  downstream: [ 'Answer:China' ],
                  obj: { component_name: 'Begin', params: {} },
                  upstream: []
                }
              },
              answer: [],
              history: [],
              messages: [],
              path: [],
              reference: []
            }
          });
          this.$message.success('创建成功');
          this.createDialogVisible = false;
          this.loadAgents();
        } catch (e) {
          let msg = e?.response?.data?.message || e.message || '创建失败';
          this.$message.error(msg);
        } finally {
          this.createLoading = false;
        }
      });
    },

    // 编辑Agent - 直接进入工作流编辑器
    editAgent(agent) {
      // 兼容后端返回的agent没有title字段的情况
      const safeAgent = {
        ...agent,
        title: agent.title
      };
      console.log('编辑的智能体为：',safeAgent)
      // 触发事件通知父组件切换到工作流编辑器并加载agent数据
      this.$emit('edit-agent', safeAgent);
    },

    // 查看Agent详情
    viewAgent(agent) {
      this.selectedAgent = agent;
      this.dslDisplayText = JSON.stringify(agent.dsl || {}, null, 2);
      this.detailVisible = true;
    },

    // 删除Agent
    async deleteAgent(agent) {
      try {
        await this.$confirm(`确定要删除Agent "${agent.title}" 吗？`, '确认删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });

        await deleteAgent(agent.id);
        this.$message.success('删除成功');
        this.loadAgents();
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除Agent失败:', error);
          this.$message.error('删除Agent失败');
        }
      }
    },



    // 分页处理
    handleSizeChange(size) {
      this.pagination.page_size = size;
      this.pagination.page = 1;
      this.loadAgents();
    },

    handleCurrentChange(page) {
      this.pagination.page = page;
      this.loadAgents();
    },

    // 获取节点数量
    getNodeCount(agent) {
      if (agent.dsl && agent.dsl.graph && agent.dsl.graph.nodes) {
        return agent.dsl.graph.nodes.length;
      }
      return 0;
    },

    // 获取连接数量
    getEdgeCount(agent) {
      if (agent.dsl && agent.dsl.graph && agent.dsl.graph.edges) {
        return agent.dsl.graph.edges.length;
      }
      return 0;
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '';
      return new Date(dateString).toLocaleString();
    }
  }
};
</script>

<style scoped>
.agent-management {
  padding: 20px;
  background: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-header h2 {
  margin: 0;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.agent-cards {
  margin-bottom: 20px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.empty-state i {
  font-size: 48px;
  color: #c0c4cc;
  margin-bottom: 16px;
}

.empty-state p {
  color: #909399;
  font-size: 16px;
  margin: 0;
}

.cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.agent-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  overflow: hidden;
  border: 1px solid #e4e7ed;
}

.agent-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  border-color: #409eff;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
}

.agent-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
}

.agent-title i {
  margin-right: 8px;
  font-size: 18px;
}

.card-actions {
  display: flex;
  gap: 8px;
}

.card-actions .el-button {
  color: #fff;
  border: none;
  background: transparent;
}

.card-actions .el-button:hover {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
}

.card-content {
  padding: 20px;
}

.agent-info {
  margin-bottom: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
  color: #909399;
}

.info-item i {
  margin-right: 6px;
  width: 14px;
}

.agent-stats {
  display: flex;
  gap: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stat-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: #409eff;
}

.card-footer {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 12px 20px;
  background: #fafafa;
  border-top: 1px solid #f0f0f0;
}

.pagination-container {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.agent-detail {
  padding: 20px 0;
}

.dsl-section {
  margin-top: 20px;
}

.dsl-section h4 {
  margin-bottom: 10px;
  color: #333;
}

.dialog-footer {
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .cards-container {
    grid-template-columns: 1fr;
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .agent-stats {
    flex-direction: column;
    gap: 8px;
  }
}
</style> 