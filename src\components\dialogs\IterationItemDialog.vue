<template>
  <el-dialog
    :visible.sync="dialogVisible"
    :title="dialogTitle"
    width="500px"
    :close-on-click-modal="false"
  >
    <div class="iteration-item-edit-form">
      <div class="node-description">
        循环项是循环节点内部的可配置元素，用于定义每次迭代的行为和处理的数据。
      </div>
      <el-form label-position="top">
        <el-form-item label="标题">
          <el-input v-model="currentItem.title" placeholder="输入循环项标题"></el-input>
        </el-form-item>
        <el-form-item label="变量名">
          <el-input v-model="currentItem.settings.variable" placeholder="输入变量名"></el-input>
        </el-form-item>
        <el-form-item label="数据源">
          <el-select v-model="currentItem.settings.source" style="width: 100%">
            <el-option label="文本片段" value="text_segments"></el-option>
            <el-option label="数组" value="array"></el-option>
            <el-option label="对象" value="object"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSave">保存</el-button>
    </span>
  </el-dialog>
</template>

<script>
/**
 * 循环项编辑对话框组件
 * 
 * 该组件用于创建或编辑工作流中循环节点内的循环项。
 * 允许用户设置循环项的标题和相关设置，如变量名和数据源。
 * 循环项是循环节点内部的可配置元素，用于定义每次迭代的行为。
 * 通过props接收初始数据，并通过事件发送编辑结果。
 */
export default {
  name: 'IterationItemDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    itemData: {
      type: Object,
      default: () => ({
        id: '',
        title: '',
        settings: {
          variable: '',
          source: 'text_segments'
        },
        onSave: null
      })
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(value) {
        if (!value) {
          this.$emit('close');
        }
      }
    },
    dialogTitle() {
      return this.currentItem.id ? '编辑循环项' : '添加循环项';
    }
  },
  data() {
    return {
      currentItem: {
        id: '',
        title: '',
        settings: {
          variable: '',
          source: 'text_segments'
        },
        onSave: null
      }
    };
  },
  watch: {
    itemData: {
      handler(newVal) {
        // 深拷贝以避免直接修改props
        this.currentItem = {
          ...newVal,
          settings: { ...newVal.settings }
        };
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    handleSave() {
      console.log('Saving iteration item:', this.currentItem.title, this.currentItem.settings);
      if (this.currentItem.onSave) {
        this.currentItem.onSave(this.currentItem.title, this.currentItem.settings);
      }
      this.$emit('close');
    },
    handleCancel() {
      this.$emit('close');
    }
  }
}
</script>

<style scoped>
.iteration-item-edit-form {
  padding: 0 20px;
}

.node-description {
  background-color: #E8F7FF;
  color: #666;
  padding: 10px 15px;
  border-radius: 4px;
  margin-bottom: 15px;
  border-left: 3px solid #1890FF;
  font-size: 14px;
  line-height: 1.5;
}
</style> 