// DATASET MANAGEMENT API 封装
// 这里将封装数据集的创建、查询、删除等接口
import ragflowRequest, { RAGFLOW_API_KEY } from './index';

/**
 * 创建数据集
 * @param {Object} data - 数据集配置项，如 { name, avatar, description, embedding_model, permission, chunk_method, parser_config }
 * @returns {Promise}
 */
export function createDataset(data) {
  return ragflowRequest.post(
    '/api/v1/datasets',
    data,
    {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${RAGFLOW_API_KEY}`
      }
    }
  );
}

/**
 * 删除数据集
 * @param {Array<string>|null} ids - 要删除的数据集ID列表，null表示删除全部，空数组表示不删除
 * @returns {Promise}
 */
export function deleteDatasets(ids) {
  return ragflowRequest.delete(
    '/api/v1/datasets',
    {
      data: { ids },
      headers: {
        'Authorization': `Bearer ${RAGFLOW_API_KEY}`,
        'Content-Type': 'application/json'
      }
    }
  );
}

/**
 * 更新数据集配置
 * @param {string} datasetId - 数据集ID
 * @param {Object} data - 要更新的字段，如 { name, avatar, description, embedding_model, permission, chunk_method, pagerank, parser_config }
 * @returns {Promise}
 */
export function updateDataset(datasetId, data) {
  return ragflowRequest.put(
    `/api/v1/datasets/${datasetId}`,
    data,
    {
      headers: {
        'Authorization': `Bearer ${RAGFLOW_API_KEY}`,
        'Content-Type': 'application/json'
      }
    }
  );
}

/**
 * 获取数据集列表
 * @param {Object} params - 查询参数，如 { page, page_size, orderby, desc, name, id }
 * @returns {Promise}
 */
export function listDatasets(params = {}) {
  return ragflowRequest.get(
    '/api/v1/datasets',
    {
      params,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${RAGFLOW_API_KEY}`
      }
    }
  );
}

// TODO: 可继续封装数据集的查询、删除等API 