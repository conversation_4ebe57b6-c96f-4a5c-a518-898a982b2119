<template>
  <transition name="fade">
    <div v-show="visible" class="global-tooltip" :style="tooltipStyle">
      <div class="tooltip-content">{{ content }}</div>
    </div>
  </transition>
</template>

<script>
export default {
  name: 'GlobalTooltip',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    tooltipStyles: {
      type: Object,
      default: () => ({
        top: '0px',
        left: '0px'
      })
    },
    content: {
      type: String,
      default: ''
    }
  },
  computed: {
    tooltipStyle() {
      return {
        ...this.tooltipStyles
      };
    }
  }
}
</script>

<style scoped>
.global-tooltip {
  position: fixed;
  max-width: 300px;
  min-width: 150px;
  background-color: rgba(33, 33, 33, 0.8); /* 暗色背景 */
  color: #fff;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  padding: 8px 12px;
  z-index: 10000; /* 使用极高的z-index确保在最上层 */
  pointer-events: none;
  font-size: 14px;
  line-height: 1.5;
}

.global-tooltip::after {
  content: '';
  position: absolute;
  left: -6px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
  border-right: 6px solid rgba(33, 33, 33, 0.9); /* 匹配提示框背景色 */
}

.tooltip-content {
  color: #fff;
  font-size: 13px;
  line-height: 1.4;
  word-break: break-word;
}

/* 淡入淡出动画 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.2s;
}
.fade-enter, .fade-leave-to {
  opacity: 0;
}
</style> 