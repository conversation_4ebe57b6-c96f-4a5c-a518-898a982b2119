// CHAT ASSISTANT MANAGEMENT API 封装
// 这里将封装 chat assistant 的创建、查询、更新等接口
import ragflowRequest, { RAGFLOW_API_KEY } from './index';

/**
 * 创建 Chat Assistant
 * @param {Object} data - 请求体参数，包括 name, avatar, dataset_ids, llm, prompt 等
 * @returns {Promise} - 返回创建结果
 * @example
 * createChatAssistant({
 *   name: 'new_chat_1',
 *   avatar: '',
 *   dataset_ids: ['0b2cbc8c877f11ef89070242ac120005'],
 *   llm: { model_name: 'qwen-plus@Tongyi-Qianwen', temperature: 0.1 },
 *   prompt: { prompt: 'You are an assistant.' }
 * })
 */
export function createChatAssistant(data) {
  return ragflowRequest.post(
    '/api/v1/chats',
    data,
    {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${RAGFLOW_API_KEY}`
      }
    }
  );
} 

/**
 * 更新 Chat Assistant
 * @param {string} chatId - 要更新的 chat assistant 的 ID
 * @param {Object} data - 请求体参数，包括 name, avatar, dataset_ids, llm, prompt 等
 * @returns {Promise} - 返回更新结果
 * @example
 * updateChatAssistant('b1f2f15691f911ef81180242ac120003', {
 *   name: 'Test',
 *   avatar: '',
 *   dataset_ids: ['0b2cbc8c877f11ef89070242ac120005'],
 *   llm: { model_name: 'qwen-plus@Tongyi-Qianwen', temperature: 0.1 },
 *   prompt: { prompt: 'You are an assistant.' }
 * })
 */
export function updateChatAssistant(chatId, data) {
  return ragflowRequest.put(
    `/api/v1/chats/${chatId}`,
    data,
    {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${RAGFLOW_API_KEY}`
      }
    }
  );
} 

/**
 * 删除 Chat Assistants
 * @param {string[]} ids - 要删除的 chat assistant 的 ID 列表。如果不传，删除所有 chat assistants（危险操作）。
 * @returns {Promise} - 返回删除结果
 * @example
 * deleteChatAssistants(['test_1', 'test_2'])
 */
export function deleteChatAssistants(ids) {
  return ragflowRequest.delete(
    '/api/v1/chats',
    {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${RAGFLOW_API_KEY}`
      },
      data: { ids }
    }
  );
} 

/**
 * 获取 Chat Assistant 列表
 * @param {Object} params - 查询参数，可包含 page, page_size, orderby, desc, id, name
 * @returns {Promise} - 返回 chat assistant 列表
 * @example
 * listChatAssistants({ page: 1, page_size: 30, orderby: 'create_time', desc: true })
 */
export function listChatAssistants(params = {}) {
  return ragflowRequest.get(
    '/api/v1/chats',
    {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${RAGFLOW_API_KEY}`
      },
      params
    }
  );
} 