<template>
  <el-drawer
    :visible.sync="drawerVisible"
    :with-header="true"
    title="AI 对话测试"
    size="40%"
    direction="rtl"
    :before-close="handleClose"
    class="chat-drawer"
  >
    <div class="chat-container">
      <!-- 对话历史区域 -->
      <div class="chat-messages" ref="messagesContainer">
        <div v-if="messages.length === 0" class="empty-chat">
          <i class="el-icon-chat-dot-round"></i>
          <p>开始与AI对话吧！</p>
        </div>
        
        <div 
          v-for="(message, index) in messages" 
          :key="index" 
          class="message-item"
          :class="{ 'user-message': message.role === 'user', 'ai-message': message.role === 'assistant' }"
        >
          <div class="message-avatar">
            <i :class="message.role === 'user' ? 'el-icon-user' : 'el-icon-cpu'"></i>
          </div>
          <div class="message-content">
            <div class="message-text">
              <div v-html="renderMarkdown(message.content)"></div>
              <!-- 模拟流式输出时显示光标 -->
              <span v-if="message.isStreaming" class="streaming-cursor">|</span>
            </div>
            <div class="message-time">{{ formatTime(message.timestamp) }}</div>
          </div>
        </div>
        
        <!-- AI正在输入指示器 -->
        <div v-if="isTyping" class="message-item ai-message typing">
          <div class="message-avatar">
            <i class="el-icon-cpu"></i>
          </div>
          <div class="message-content">
            <div class="typing-indicator">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 配置选项区域 -->
      <div class="chat-config-area">
        <el-row :gutter="10" type="flex" align="middle">
          <el-col :span="12">
            <el-tooltip content="启用后将始终通过Agent工作流处理，确保工作流逻辑正确执行" placement="top">
              <el-switch
                v-model="alwaysUseWorkflow"
                active-text="使用工作流"
                inactive-text="混合模式"
                active-color="#13ce66"
                inactive-color="#ff4949"
              ></el-switch>
            </el-tooltip>
          </el-col>
          <el-col :span="12">
            <el-text size="small" type="info">
              {{ alwaysUseWorkflow ? '✅ 通过Agent工作流处理' : '⚠️ 可能绕过工作流' }}
            </el-text>
          </el-col>
        </el-row>
      </div>

      <!-- 输入区域 -->
      <div class="chat-input-area">
        <div class="input-container">
          <el-input
            v-model="inputMessage"
            type="textarea"
            :rows="3"
            placeholder="输入您的问题..."
            @keydown.enter.ctrl="sendMessage"
            :disabled="isTyping"
            resize="none"
          ></el-input>
          <div class="input-actions">
            <el-button
              type="primary"
              size="small"
              @click="sendMessage"
              :loading="isTyping"
              :disabled="!inputMessage.trim()"
            >
              发送 (Ctrl+Enter)
            </el-button>
            <el-button
              size="small"
              @click="clearChat"
              :disabled="messages.length === 0"
            >
              清空
            </el-button>
            <el-dropdown trigger="click" size="small">
              <el-button size="small" type="info">
                调试 <i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item @click.native="logConversationData">
                  <i class="el-icon-view"></i> 查看对话数据
                </el-dropdown-item>
                <el-dropdown-item @click.native="logRetrievalDetails">
                  <i class="el-icon-search"></i> 查看检索详情
                </el-dropdown-item>
                <el-dropdown-item @click.native="exportConversationData">
                  <i class="el-icon-download"></i> 导出对话数据
                </el-dropdown-item>
                <el-dropdown-item @click.native="getSavedConversationData">
                  <i class="el-icon-folder-opened"></i> 获取保存数据
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script>
// import { agentCompletionOpenAI } from '@/api/openai-compatible-API'; // 🔥 注释掉OpenAI兼容API
import { converseWithAgentNative } from '@/api/session-management-API';
// import { retrieveChunks } from '@/api/chunk-management-API'; // 🔥 注释掉混合检索API
// import { extractKeywords as extractKeywordsUtil } from '@/utils/keywordExtractor.js'; // 🔥 注释掉关键词提取

export default {
  name: 'ChatDrawer',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 当前工作流的Agent ID
    agentId: {
      type: String,
      default: null
    },
    // 当前工作流的DSL数据
    workflowDSL: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      inputMessage: '',
      isTyping: false,
      messages: [],

      // 🔥 原生Agent API会话管理
      currentSessionId: null, // 当前会话ID
      useNativeAPI: true,     // 是否使用原生Agent API（默认启用）

      // 对话保存相关
      lastSaveTime: null,
      saveStatus: 'idle', // idle, saving, saved, error

      // 检索结果缓存
      retrievalResults: [], // 存储每次检索的结果
      currentReference: [],  // 当前对话轮次的引用数据

      // 混合检索配置
      useHybridRetrieval: false, // 是否使用混合检索模式（绕过工作流）
      alwaysUseWorkflow: true    // 是否始终使用Agent工作流
    };
  },
  computed: {
    drawerVisible: {
      get() {
        return this.visible;
      },
      set(value) {
        if (!value) {
          this.$emit('close');
        }
      }
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        console.log('[聊天API调试] ChatDrawer打开');
        console.log('[聊天API调试] 接收到的Agent ID:', this.agentId);
        console.log('[聊天API调试] 接收到的工作流DSL:', this.workflowDSL);

        // 初始化欢迎消息
        this.initializeChat();

        this.$nextTick(() => {
          this.scrollToBottom();
        });
      }
    },
    agentId(newVal) {
      console.log('[聊天API调试] Agent ID变化:', newVal);
    }
  },

  methods: {
    handleClose() {
      this.$emit('close');
    },

    // 初始化聊天
    async initializeChat() {
      // 清空之前的消息
      this.messages = [];

      // 🔥 重置会话ID，下次对话将创建新会话
      this.currentSessionId = null;
      console.log('[会话管理] 初始化聊天，会话ID已重置');

      // 检查是否有Agent ID
      if (!this.agentId) {
        this.messages.push({
          role: 'assistant',
          content: '当前工作流未保存为Agent，请先保存工作流后再进行对话测试。',
          timestamp: new Date()
        });
        return;
      }

      // 🔥 自动调用一次原生Agent API获取真正的开场白和session_id
      try {
        console.log('[初始化] 自动调用Agent API获取开场白...');
        await this.initializeAgentSession();
      } catch (error) {
        console.error('[初始化] 获取开场白失败:', error);
        // 如果获取开场白失败，显示默认消息
        const fallbackMessage = this.getWelcomeMessageFromDSL();
        this.messages.push({
          role: 'assistant',
          content: fallbackMessage || 'Hi! I\'m your smart assistant. What can I do for you?',
          timestamp: new Date()
        });
      }
    },

    // 🔥 初始化Agent会话，获取开场白和session_id
    async initializeAgentSession() {
      try {
        // 发送空请求或Begin组件参数来初始化会话
        const requestData = {
          sync_dsl: true, // 同步最新DSL
          stream: false
        };

        // 检查Begin组件是否需要参数
        const beginParams = this.getBeginComponentParams();
        if (beginParams && Object.keys(beginParams).length > 0) {
          // 如果Begin组件需要参数，传递这些参数
          Object.assign(requestData, beginParams);
          console.log('[初始化] Begin组件需要参数:', beginParams);
        } else {
          // 如果Begin组件不需要参数，发送空对象来创建会话
          console.log('[初始化] Begin组件不需要参数，创建新会话');
        }

        console.log('[初始化] 发送初始化请求:', requestData);

        // 调用原生Agent API
        const response = await converseWithAgentNative(this.agentId, requestData);

        console.log('[初始化] 收到初始化响应:', response);

        // 处理响应
        if (response.data && response.data.data) {
          const responseData = response.data.data;

          // 保存session_id
          if (responseData.session_id) {
            this.currentSessionId = responseData.session_id;
            console.log('[初始化] 获取到session_id:', this.currentSessionId);
          }

          // 显示开场白（如果有）
          if (responseData.answer && responseData.answer.trim()) {
            this.messages.push({
              role: 'assistant',
              content: responseData.answer,
              timestamp: new Date(),
              sessionId: responseData.session_id,
              reference: responseData.reference
            });
            console.log('[初始化] 显示开场白:', responseData.answer);
          } else {
            // 如果没有开场白，显示默认消息
            const fallbackMessage = this.getWelcomeMessageFromDSL();
            this.messages.push({
              role: 'assistant',
              content: fallbackMessage || 'Hi! I\'m your smart assistant. What can I do for you?',
              timestamp: new Date()
            });
          }
        }

      } catch (error) {
        console.error('[初始化] Agent会话初始化失败:', error);
        throw error;
      }
    },

    // 从DSL中获取Begin节点的开场白
    getWelcomeMessageFromDSL() {
      try {
        // 检查DSL数据是否存在
        if (!this.workflowDSL || !this.workflowDSL.components) {
          console.log('[聊天API调试] DSL数据不存在，使用默认开场白');
          return "Hi! I'm your smart assistant. What can I do for you?";
        }

        // 查找Begin节点
        const beginComponent = this.workflowDSL.components.begin ||
                              this.workflowDSL.components['begin'] ||
                              Object.values(this.workflowDSL.components).find(comp =>
                                comp.obj && comp.obj.component_name === 'Begin'
                              );

        if (beginComponent && beginComponent.obj) {
          // 优先从output.content中获取
          if (beginComponent.obj.output && beginComponent.obj.output.content) {
            if (typeof beginComponent.obj.output.content === 'string') {
              console.log('[聊天API调试] 从Begin节点output.content获取开场白:', beginComponent.obj.output.content);
              return beginComponent.obj.output.content;
            } else if (beginComponent.obj.output.content['0'] && beginComponent.obj.output.content['0'].content) {
              console.log('[聊天API调试] 从Begin节点output.content.0.content获取开场白:', beginComponent.obj.output.content['0'].content);
              return beginComponent.obj.output.content['0'].content;
            }
          }

          // 其次从params.prologue中获取
          if (beginComponent.obj.params && beginComponent.obj.params.prologue) {
            console.log('[聊天API调试] 从Begin节点params.prologue获取开场白:', beginComponent.obj.params.prologue);
            return beginComponent.obj.params.prologue;
          }
        }

        console.log('[聊天API调试] 未找到Begin节点或开场白内容，使用默认开场白');
        return "Hi! I'm your smart assistant. What can I do for you?";
      } catch (error) {
        console.error('[聊天API调试] 获取开场白时出错:', error);
        return "Hi! I'm your smart assistant. What can I do for you?";
      }
    },

    // 🔥 获取Begin组件的参数（用于初始化会话）
    getBeginComponentParams() {
      try {
        if (!this.workflowDSL || !this.workflowDSL.components) {
          return {};
        }

        // 查找Begin节点
        const beginComponent = this.workflowDSL.components.begin ||
                              this.workflowDSL.components['begin'] ||
                              Object.values(this.workflowDSL.components).find(comp =>
                                comp.obj && comp.obj.component_name === 'Begin'
                              );

        if (beginComponent && beginComponent.obj && beginComponent.obj.params) {
          const params = { ...beginComponent.obj.params };

          // 移除prologue，因为它不是API参数，是内部配置
          delete params.prologue;

          console.log('[DSL解析] Begin组件参数:', params);
          return params;
        }

        return {};
      } catch (error) {
        console.error('[DSL解析] 获取Begin组件参数失败:', error);
        return {};
      }
    },

    async sendMessage() {
      if (!this.inputMessage.trim() || this.isTyping) return;

      // 清空当前引用数据，开始新的对话轮次
      this.clearCurrentReference();

      const userMessage = {
        role: 'user',
        content: this.inputMessage.trim(),
        timestamp: new Date()
      };

      console.log('[聊天API调试] 发送用户消息:', userMessage.content);
      console.log('[聊天API调试] 当前Agent ID:', this.agentId);
      
      this.messages.push(userMessage);
      this.inputMessage = '';
      this.isTyping = true;
      
      this.$nextTick(() => {
        this.scrollToBottom();
      });
      
      try {
        // 检查是否有agentId
        if (!this.agentId) {
          throw new Error('当前工作流未保存为Agent，请先保存为Agent后再进行对话测试');
        }

        // 检查工作流是否包含Answer组件
        const hasAnswerComponent = this.checkForAnswerComponent();
        if (!hasAnswerComponent) {
          throw new Error('工作流中缺少Answer组件，请添加Answer组件后再保存并测试对话');
        }

        // 🔥 只使用纯原生Agent API发送消息
        console.log('[聊天API调试] 使用纯原生Agent API发送消息');
        await this.sendMessageToAgentNative(userMessage.content);
      } catch (error) {
        console.error('[聊天API调试] 发送消息失败:', error);
        this.handleApiError(error);
      } finally {
        // 确保isTyping状态被重置（防止出错时状态卡住）
        if (this.isTyping) {
          this.isTyping = false;
        }
        this.$nextTick(() => {
          this.scrollToBottom();
        });

        // 保存对话数据
        this.saveConversationData();
      }
    },
    
    async sendMessageToAgent(userInput) {
      try {
        // 首先执行关键词提取（如果工作流中包含关键词组件）
        console.log('[聊天API调试] 开始执行关键词提取');
        const extractedKeywords = await this.executeKeywordExtractionIfNeeded(userInput);
        console.log('[聊天API调试] 🔍 关键词提取结果:', extractedKeywords);

        // 如果成功提取了关键词，显示给用户
        if (extractedKeywords && extractedKeywords.trim()) {
          console.log('[聊天API调试] ✅ 成功提取关键词，将用于优化处理');
          this.$message({
            message: `🔍 提取的关键词: ${extractedKeywords}`,
            type: 'info',
            duration: 3000
          });
        }

        // 检查工作流中是否包含Retrieval组件
        console.log('[聊天API调试] 开始检查是否包含Retrieval组件');
        const hasRetrievalComponent = this.checkForRetrievalComponent();

        if (hasRetrievalComponent) {
          if (this.useHybridRetrieval && !this.alwaysUseWorkflow) {
            console.log('[聊天API调试] ✅ 检测到Retrieval组件，使用混合检索模式（绕过工作流）');
            // 使用原始的混合检索逻辑
            return await this.sendMessageWithHybridRetrieval(userInput, extractedKeywords);
          } else {
            console.log('[聊天API调试] ✅ 检测到Retrieval组件，通过Agent工作流处理');
            console.log('[聊天API调试] 📝 关键词将作为上下文信息传递给工作流');

            // 如果有关键词，将其作为上下文信息添加到用户输入中
            let enhancedUserInput = userInput;
            if (extractedKeywords && extractedKeywords.trim()) {
              enhancedUserInput = `${userInput}\n\n[关键词提示: ${extractedKeywords}]`;
              console.log('[聊天API调试] 增强后的用户输入:', enhancedUserInput);
            }

            // 更新最后一条消息为增强后的输入
            userInput = enhancedUserInput;
          }
        } else {
          console.log('[聊天API调试] ❌ 未检测到Retrieval组件，使用普通模式');
          // 即使在普通模式下，也显示关键词提取结果（如果有的话）
          if (extractedKeywords && extractedKeywords.trim()) {
            console.log('[聊天API调试] 📝 在普通模式下记录关键词提取结果');
          }
        }

        // 🔥 使用原生Agent API替代OpenAI兼容API
        const requestData = {
          question: userInput,
          stream: false,
          sync_dsl: true, // 🔥 关键：同步最新DSL
        };

        // 如果有现有会话，添加session_id
        if (this.currentSessionId) {
          requestData.session_id = this.currentSessionId;
        }

        console.log('[聊天API调试] 使用原生Agent API发送请求:', {
          agentId: this.agentId,
          requestData,
          hasSessionId: !!this.currentSessionId
        });

        console.log('[聊天API调试] 当前工作流DSL结构:', {
          hasComponents: !!this.workflowDSL?.components,
          componentCount: this.workflowDSL?.components ? Object.keys(this.workflowDSL.components).length : 0,
          componentTypes: this.workflowDSL?.components ? Object.keys(this.workflowDSL.components).map(key => this.workflowDSL.components[key].obj?.component_name) : []
        });

        // 🔥 调用原生Agent API
        const response = await converseWithAgentNative(this.agentId, requestData);

        console.log('[聊天API调试] 收到响应:', response);
        console.log('[聊天API调试] 响应数据:', response.data);

        // 处理返回（统一处理普通响应和混合检索响应）
        return this.handleApiResponse(response);

      } catch (error) {
        console.error('[聊天API调试] Agent OpenAI兼容API调用失败:', error);

        // 立即关闭"正在输入"指示器
        this.isTyping = false;

        // 如果有正在流式输出的消息，移除它
        const lastMessage = this.messages[this.messages.length - 1];
        if (lastMessage && lastMessage.isStreaming) {
          this.messages.pop();
        }

        this.handleApiError(error);
        throw error;
      }
    },

    // 🔥 纯原生Agent API方法（简化版，无混合检索）
    async sendMessageToAgentNative(userInput) {
      try {
        console.log('[原生Agent API] 开始调用纯原生Agent API');
        console.log('[原生Agent API] 用户输入:', userInput);
        console.log('[原生Agent API] 当前会话ID:', this.currentSessionId);

        // 🔥 使用原生Agent API替代OpenAI兼容API
        const requestData = {
          question: userInput,
          stream: false,
          sync_dsl: true, // 🔥 关键：同步最新DSL
        };

        // 如果有现有会话，添加session_id
        if (this.currentSessionId) {
          requestData.session_id = this.currentSessionId;
        }

        console.log('[原生Agent API] 发送请求数据:', requestData);

        // 🔥 调用原生Agent API
        const response = await converseWithAgentNative(this.agentId, requestData);

        console.log('[原生Agent API] 收到响应:', response);

        // 处理返回
        return this.handleApiResponse(response);

      } catch (error) {
        console.error('[原生Agent API] 调用失败:', error);

        // 立即关闭"正在输入"指示器
        this.isTyping = false;

        // 如果有正在流式输出的消息，移除它
        const lastMessage = this.messages[this.messages.length - 1];
        if (lastMessage && lastMessage.isStreaming) {
          this.messages.pop();
        }

        this.handleApiError(error);
        throw error;
      }
    },

    // 统一处理API响应
    async handleApiResponse(response) {
      try {
        let fullContent = '';
        let sessionId = null;
        let reference = null;

        // 🔥 处理原生Agent API响应格式
        if (response.data && response.data.data) {
          const responseData = response.data.data;

          // 正常对话响应
          if (typeof responseData === 'object' && responseData.answer !== undefined) {
            fullContent = responseData.answer || '';
            sessionId = responseData.session_id;
            reference = responseData.reference;

            // 🔥 保存会话ID用于后续对话
            if (sessionId && !this.currentSessionId) {
              this.currentSessionId = sessionId;
              console.log('[会话管理] 保存新会话ID:', sessionId);
            }

            console.log('[聊天API调试] 原生Agent API响应解析:', {
              answer: fullContent,
              sessionId,
              hasReference: !!reference
            });

          } else if (responseData === true) {
            // 流式输出结束标志，忽略
            console.log('[聊天API调试] 收到流式输出结束标志，忽略');
            return;
          } else {
            throw new Error('原生Agent API响应格式错误：data.data字段格式不正确');
          }
        }
        // 兼容OpenAI格式（向后兼容）
        else if (response.data && response.data.choices && response.data.choices[0]?.message?.content) {
          fullContent = response.data.choices[0].message.content;
          console.log('[聊天API调试] 使用OpenAI兼容格式解析');
        }
        else {
          console.error('[聊天API调试] API返回数据格式错误，完整响应:', response);
          throw new Error(`API返回数据格式错误: ${JSON.stringify(response.data)}`);
        }



        // 立即关闭"正在输入"指示器
        this.isTyping = false;

        // 创建一个空的AI消息用于模拟流式输出
        const aiMessage = {
          role: 'assistant',
          content: '',
          timestamp: new Date(),
          isStreaming: true,
          sessionId: sessionId, // 保存会话ID
          reference: reference   // 保存引用信息
        };
        this.messages.push(aiMessage);

        console.log('[聊天API调试] 开始模拟流式输出:', fullContent);

        // 模拟流式输出效果
        await this.simulateStreamingOutput(aiMessage, fullContent);

      } catch (error) {
        console.error('[聊天API调试] 响应处理失败:', error);

        // 立即关闭"正在输入"指示器
        this.isTyping = false;

        // 如果有正在流式输出的消息，移除它
        const lastMessage = this.messages[this.messages.length - 1];
        if (lastMessage && lastMessage.isStreaming) {
          this.messages.pop();
        }

        this.handleApiError(error);
        throw error;
      }
    },

    // 模拟流式输出效果
    async simulateStreamingOutput(aiMessage, fullContent) {
      // 按字符分割，但在标点符号处稍作停顿
      const chars = fullContent.split('');

      for (let i = 0; i < chars.length; i++) {
        // 添加字符到消息内容
        aiMessage.content += chars[i];

        // 实时滚动到底部
        this.$nextTick(() => {
          this.scrollToBottom();
        });

        // 根据字符类型设置不同的延迟
        let delay = 20; // 基础延迟
        const char = chars[i];

        if (char === '。' || char === '！' || char === '？' || char === '.' || char === '!' || char === '?') {
          delay = 200; // 句号等停顿较长
        } else if (char === '，' || char === '；' || char === '：' || char === ',' || char === ';' || char === ':') {
          delay = 100; // 逗号等停顿中等
        } else if (char === ' ') {
          delay = 30; // 空格稍作停顿
        } else if (/[\u4e00-\u9fa5]/.test(char)) {
          delay = 25; // 中文字符
        } else {
          delay = 15; // 英文字符较快
        }

        // 延迟一段时间再显示下一个字符
        await new Promise(resolve => setTimeout(resolve, delay));
      }

      // 输出完成，移除流式状态
      aiMessage.isStreaming = false;

      console.log('[聊天API调试] 模拟流式输出完成');

      // 最后滚动到底部
      this.$nextTick(() => {
        this.scrollToBottom();
      });

      // 保存对话数据
      this.saveConversationData();
    },

    // 检查工作流中是否包含Retrieval组件
    checkForRetrievalComponent() {
      try {
        if (!this.workflowDSL || !this.workflowDSL.components) {
          console.log('[聊天API调试] 没有workflowDSL或components');
          return false;
        }

        console.log('[聊天API调试] 检查工作流组件:', Object.keys(this.workflowDSL.components));

        // 检查是否有Retrieval类型的组件
        const retrievalComponents = Object.values(this.workflowDSL.components).filter(component =>
          component.obj?.component_name === 'Retrieval'
        );

        console.log('[聊天API调试] 找到的Retrieval组件:', retrievalComponents.length);

        if (retrievalComponents.length > 0) {
          console.log('[聊天API调试] Retrieval组件详情:', retrievalComponents[0]);
        }

        const hasRetrieval = retrievalComponents.length > 0;
        console.log('[聊天API调试] Retrieval组件检查结果:', hasRetrieval);
        return hasRetrieval;
      } catch (error) {
        console.error('[聊天API调试] 检查Retrieval组件时出错:', error);
        return false;
      }
    },

    // 使用混合检索模式发送消息
    async sendMessageWithHybridRetrieval(userInput, extractedKeywords = null) {
      try {
        console.log('[混合检索] 开始混合检索处理');
        console.log('[混合检索] 用户输入:', userInput);
        console.log('[混合检索] 提取的关键词:', extractedKeywords);

        // 1. 首先尝试使用原始用户输入进行检索
        let retrievalResult = await this.performDirectRetrieval(userInput, extractedKeywords);

        // 2. 如果原始查询没有结果且有关键词，尝试使用关键词检索
        if ((!retrievalResult || retrievalResult.length === 0) && extractedKeywords) {
          console.log('[混合检索] 原始查询无结果，尝试使用关键词检索:', extractedKeywords);
          retrievalResult = await this.performDirectRetrieval(extractedKeywords, extractedKeywords);
        }

        if (!retrievalResult || retrievalResult.length === 0) {
          // 如果检索失败或无结果，使用关键词增强的普通对话
          console.log('[混合检索] 检索无结果，使用关键词增强的普通对话');

          // 构建包含关键词信息的增强提示
          let enhancedPrompt = userInput;
          if (extractedKeywords) {
            enhancedPrompt = `用户问题：${userInput}\n关键词：${extractedKeywords}\n\n请基于这些关键词提供详细的回答。`;
          }

          return await this.sendNormalMessage(enhancedPrompt);
        }

        // 2. 将检索结果整合到消息中
        const enhancedInput = this.buildEnhancedInput(userInput, retrievalResult);

        // 3. 发送增强后的消息
        const response = await this.sendEnhancedMessage(enhancedInput);
        console.log('[混合检索] 混合检索完成，返回响应');
        return response;

      } catch (error) {
        console.error('[混合检索] 混合检索处理失败:', error);
        // 出错时回退到普通对话
        return await this.sendNormalMessage(userInput);
      }
    },

    // 执行关键词提取（如果工作流中包含关键词组件）
    async executeKeywordExtractionIfNeeded(userInput) {
      try {
        console.log('[聊天关键词] 检查是否需要执行关键词提取');

        // 检查工作流中是否包含关键词提取组件
        const keywordComponent = Object.values(this.workflowDSL.components || {}).find(
          component => component.obj?.component_name === 'KeywordExtract'
        );

        if (!keywordComponent) {
          console.log('[聊天关键词] 工作流中未找到关键词提取组件，跳过关键词提取');
          return null;
        }

        console.log('[聊天关键词] 找到关键词提取组件:', keywordComponent);

        // 构造对话历史格式的输入
        const dialogHistory = this.buildDialogHistoryForKeywordExtraction(userInput);
        console.log('[聊天关键词] 构造的对话历史:', dialogHistory);

        // 使用新的关键词提取工具
        const topN = keywordComponent.obj.params?.top_n || 3;
        console.log('[聊天关键词] 使用智能关键词提取工具，TopN:', topN);

        // 🔥 注释掉关键词提取工具调用
        // const extractedKeywords = extractKeywordsUtil(dialogHistory, topN);
        const extractedKeywords = null; // 暂时返回null

        if (extractedKeywords && extractedKeywords.trim()) {
          console.log('[聊天关键词] ✅ 关键词提取成功:', extractedKeywords);
          return extractedKeywords;
        } else {
          console.warn('[聊天关键词] ⚠️ 关键词提取结果为空');
          return null;
        }

      } catch (error) {
        console.error('[聊天关键词] 关键词提取过程中发生错误:', error);
        return null;
      }
    },

    // 为关键词提取构造对话历史
    buildDialogHistoryForKeywordExtraction(userInput) {
      console.log('[聊天关键词] 构造对话历史，用户输入:', userInput);
      console.log('[聊天关键词] 当前对话历史长度:', this.messages.length);

      // 检查是否为追问类型的输入
      const isFollowUpQuestion = this.isFollowUpQuestion(userInput);
      console.log('[聊天关键词] 是否为追问:', isFollowUpQuestion);

      if (isFollowUpQuestion && this.messages.length > 0) {
        // 如果是追问，需要包含上下文
        return this.buildContextualDialogHistory(userInput);
      } else {
        // 如果是新问题，使用简单格式
        return this.buildSimpleDialogHistory(userInput);
      }
    },

    // 判断是否为追问
    isFollowUpQuestion(userInput) {
      const followUpPatterns = [
        '再仔细', '再详细', '更详细', '具体', '深入', '进一步',
        '还有', '另外', '其他', '补充', '继续', '更多',
        '怎么', '如何', '为什么', '什么时候', '哪里',
        '能否', '可以', '可否', '是否', '有没有'
      ];

      const isShort = userInput.length < 10; // 短问题通常是追问
      const hasFollowUpKeyword = followUpPatterns.some(pattern => userInput.includes(pattern));

      return isShort || hasFollowUpKeyword;
    },

    // 构造包含上下文的对话历史
    buildContextualDialogHistory(userInput) {
      console.log('[聊天关键词] 构造上下文对话历史');

      // 获取最近的几轮对话作为上下文
      const recentMessages = this.messages.slice(-4); // 最近2轮对话
      console.log('[聊天关键词] 最近的对话:', recentMessages);

      // 获取Begin组件的开场白
      const beginComponent = this.workflowDSL.components?.begin;
      const prologue = beginComponent?.obj?.params?.prologue || 'Hi! I\'m your smart assistant. What can I do for you?';

      // 构造完整的对话历史
      let dialogHistory = `ASSISTANT:${prologue}\n`;

      // 添加最近的对话历史
      recentMessages.forEach(message => {
        if (message.type === 'user') {
          dialogHistory += `USER:${message.content}\n`;
        } else if (message.type === 'assistant') {
          // 只取回答的前100个字符，避免过长
          const shortContent = message.content.length > 100
            ? message.content.substring(0, 100) + '...'
            : message.content;
          dialogHistory += `ASSISTANT:${shortContent}\n`;
        }
      });

      // 添加当前用户输入
      dialogHistory += `USER:${userInput}`;

      console.log('[聊天关键词] 构造的上下文对话历史:', dialogHistory);
      return dialogHistory;
    },

    // 构造简单的对话历史
    buildSimpleDialogHistory(userInput) {
      console.log('[聊天关键词] 构造简单对话历史');

      // 获取Begin组件的开场白
      const beginComponent = this.workflowDSL.components?.begin;
      const prologue = beginComponent?.obj?.params?.prologue || 'Hi! I\'m your smart assistant. What can I do for you?';

      // 构造标准的对话历史格式
      const dialogHistory = `ASSISTANT:${prologue}\nUSER:${userInput}`;

      console.log('[聊天关键词] 构造的简单对话历史:', dialogHistory);
      return dialogHistory;
    },

    // 执行直接检索
    async performDirectRetrieval(question, keywords = null) {
      try {
        // 从工作流DSL中获取Retrieval组件的配置
        const retrievalComponent = Object.values(this.workflowDSL.components).find(
          component => component.obj?.component_name === 'Retrieval'
        );

        if (!retrievalComponent) {
          console.error('[混合检索] 未找到Retrieval组件配置');
          return null;
        }

        const params = retrievalComponent.obj.params;
        console.log('[混合检索] Retrieval组件参数:', params);

        // 检查kb_ids是否存在
        if (!params.kb_ids || params.kb_ids.length === 0) {
          console.error('[混合检索] 知识库ID列表为空，请检查Retrieval组件配置');
          return null;
        }

        const testParams = {
          question: question,
          dataset_ids: params.kb_ids,
          similarity_threshold: Math.min(params.similarity_threshold || 0.3, 0.3), // 降低阈值以获得更多结果
          vector_similarity_weight: 1 - (params.keywords_similarity_weight || 0.5),
          top_k: Math.max(params.top_n || 5, 5), // 增加返回数量
          keyword: true,
          highlight: true
        };

        console.log('[混合检索] 优化后的检索参数:', testParams);

        console.log('[混合检索] 检索参数:', testParams);
        console.log('[混合检索] 使用的知识库ID:', params.kb_ids);

        // 🔥 注释掉混合检索调用
        // const response = await retrieveChunks(testParams);
        const response = { data: { code: -1, message: '混合检索已禁用' } };

        if (response.data && response.data.code === 0) {
          const chunks = response.data.data?.chunks || [];
          console.log('[混合检索] 检索成功，找到', chunks.length, '个文档片段');

          // 保存检索结果到缓存
          this.saveRetrievalResults(chunks, keywords || question);

          return chunks;
        } else {
          console.error('[混合检索] 检索API返回错误:', response.data);
          return null;
        }
      } catch (error) {
        console.error('[混合检索] 直接检索失败:', error);
        return null;
      }
    },

    // 构建增强的输入内容
    buildEnhancedInput(userInput, chunks) {
      if (!chunks || chunks.length === 0) {
        return userInput;
      }

      // 构建类似RAGFlow的检索结果格式
      let enhancedContent = `用户问题: ${userInput}\n\n相关文档内容:\n`;

      chunks.forEach((chunk, index) => {
        enhancedContent += `---\nID: ${index}\n${chunk.content}\n`;
      });

      console.log('[混合检索] 增强后的输入内容:', enhancedContent);
      return enhancedContent;
    },

    // 发送增强后的消息（通过Agent工作流处理）
    async sendEnhancedMessage(enhancedInput) {
      console.log('[混合检索] 使用Agent工作流处理增强后的输入');
      console.log('[混合检索] 增强后的输入内容:', enhancedInput);

      try {
        // 通过Agent工作流处理增强后的输入
        const messages = this.messages
          .filter(m => m.role === 'user' || m.role === 'assistant')
          .map(m => ({ role: m.role, content: m.content }));

        // 使用增强后的输入作为最新的用户消息
        messages.push({ role: 'user', content: enhancedInput });

        const model = 'deepseek-v3';

        console.log('[混合检索] 通过Agent工作流发送请求:', {
          agentId: this.agentId,
          model,
          messages,
          stream: false
        });

        // 🔥 注释掉OpenAI兼容API调用
        // const response = await agentCompletionOpenAI(this.agentId, {
        //   model,
        //   messages,
        //   stream: false
        // }, false);
        // console.log('[混合检索] Agent工作流响应:', response);
        // return this.handleApiResponse(response);

        throw new Error('OpenAI兼容API已禁用，请使用原生Agent API');

      } catch (error) {
        console.error('[混合检索] Agent工作流处理失败，回退到直接生成:', error);

        // 如果Agent工作流失败，回退到直接生成（但仍然基于检索内容）
        const generatedResponse = this.generateResponseFromRetrievedContent(enhancedInput);
        console.log('[混合检索] 回退生成的回答:', generatedResponse);

        // 模拟API响应格式
        const mockResponse = {
          data: {
            choices: [{
              message: {
                content: generatedResponse
              }
            }]
          }
        };

        // 使用统一的响应处理方法
        return await this.handleApiResponse(mockResponse);
      }
    },

    // 基于检索内容生成回答
    generateResponseFromRetrievedContent(enhancedInput) {
      // 解析增强输入，提取用户问题和检索内容
      const lines = enhancedInput.split('\n');
      const userQuestion = lines[0].replace('用户问题: ', '');

      // 提取文档内容
      let documentContent = '';
      let inDocumentSection = false;

      for (const line of lines) {
        if (line.includes('相关文档内容:')) {
          inDocumentSection = true;
          continue;
        }
        if (inDocumentSection && line.trim() && !line.startsWith('---') && !line.startsWith('ID:')) {
          documentContent += line + '\n';
        }
      }

      // 基于检索内容生成回答
      if (documentContent.trim()) {
        return this.generateAnswerFromDocument(userQuestion, documentContent.trim());
      } else {
        return `关于"${userQuestion}"的问题，我没有找到相关的文档内容。请提供更多信息或尝试其他问题。`;
      }
    },

    // 根据文档内容生成回答
    generateAnswerFromDocument(question, documentContent) {
      // 增强的模板化回答生成
      const templates = {
        '售前': () => {
          if (documentContent.includes('产品知识掌握') && documentContent.includes('客户需求挖掘')) {
            return `根据知识库文档，售前工作主要包括以下两个核心要求：

**1. 产品知识掌握**

需要全面、准确地掌握产品的各类信息，包括产品名称、规格型号、功能特点、技术参数、价格体系、优惠活动、适用场景等。对于复杂产品，还要能够清晰解释其工作原理和与同类产品的差异。

**2. 客户需求挖掘**

主动与客户沟通，通过提问等方式了解客户的基本情况（如身份、行业、规模等）、购买目的、使用场景、预算范围、对产品的特殊要求等，从而精准定位客户需求。

这两个方面是售前工作的基础，能够帮助销售人员更好地为客户提供专业的产品咨询和解决方案。`;
          }
          return this.generateGenericAnswer('售前', documentContent);
        },

        '产品': () => {
          return this.generateProductAnswer(question, documentContent);
        },

        '客户': () => {
          return this.generateCustomerAnswer(question, documentContent);
        },

        '销售': () => {
          return this.generateSalesAnswer(question, documentContent);
        }
      };

      // 检查是否有特定模板
      for (const [keyword, generator] of Object.entries(templates)) {
        if (question.includes(keyword)) {
          return generator();
        }
      }

      // 通用回答模板
      return this.generateGenericAnswer(question, documentContent);
    },

    // 生成通用格式的回答
    generateGenericAnswer(question, documentContent) {
      return `**关于"${question}"的信息**

根据知识库文档，相关内容如下：

${this.formatDocumentContent(documentContent)}

如需了解更多详细信息，请随时询问。`;
    },

    // 生成产品相关回答
    generateProductAnswer(_, documentContent) {
      return `**产品信息**

${this.formatDocumentContent(documentContent)}

如需了解产品详细规格、价格信息或技术支持，请随时询问。`;
    },

    // 生成客户相关回答
    generateCustomerAnswer(_, documentContent) {
      return `**客户服务信息**

${this.formatDocumentContent(documentContent)}

我们提供专业的售前咨询、完善的售后支持和个性化解决方案。`;
    },

    // 生成销售相关回答
    generateSalesAnswer(question, documentContent) {
      console.log('[回答生成] 开始生成销售相关回答');
      console.log('[回答生成] 用户问题:', question);
      console.log('[回答生成] 文档内容长度:', documentContent.length);

      // 检查是否为追问
      const isDetailRequest = this.isDetailRequest(question);
      console.log('[回答生成] 是否为详细信息请求:', isDetailRequest);

      // 分析用户问题的具体需求
      const isAboutBefore = question.includes('之前') || question.includes('售前');
      const isAboutAfter = question.includes('之后') || question.includes('售后');
      const isAboutProcess = question.includes('流程') || question.includes('过程');
      const isAboutSkills = question.includes('技巧') || question.includes('方法');
      const isAboutRequirements = question.includes('要求') || question.includes('标准');

      // 解析文档内容，提取关键信息
      const keyPoints = this.extractKeyPointsFromDocument(documentContent);

      // 根据问题类型生成针对性回答
      if (isAboutBefore) {
        return this.generatePreSalesAnswer(keyPoints, isDetailRequest);
      } else if (isAboutAfter) {
        return this.generatePostSalesAnswer(keyPoints, isDetailRequest);
      } else if (isAboutProcess) {
        return this.generateProcessAnswer(keyPoints, isDetailRequest);
      } else if (isAboutSkills) {
        return this.generateSkillsAnswer(keyPoints, isDetailRequest);
      } else if (isAboutRequirements) {
        return this.generateRequirementsAnswer(keyPoints, isDetailRequest);
      } else {
        return this.generateGeneralSalesAnswer(keyPoints, isDetailRequest);
      }
    },

    // 判断是否为详细信息请求
    isDetailRequest(question) {
      const detailKeywords = [
        '再仔细', '再详细', '更详细', '具体', '深入', '进一步',
        '详细说明', '具体说明', '举例', '例子', '案例',
        '怎么做', '如何操作', '步骤', '方法', '技巧'
      ];

      return detailKeywords.some(keyword => question.includes(keyword));
    },

    // 从文档中提取关键要点
    extractKeyPointsFromDocument(documentContent) {
      const points = [];

      // 按句号分割内容
      const sentences = documentContent.split(/[。！？]/);

      sentences.forEach(sentence => {
        const trimmed = sentence.trim();
        if (trimmed.length > 10) { // 过滤太短的句子
          // 识别要点类型
          if (trimmed.includes('产品知识') || trimmed.includes('掌握')) {
            points.push({ type: 'product_knowledge', content: trimmed });
          } else if (trimmed.includes('客户需求') || trimmed.includes('沟通')) {
            points.push({ type: 'customer_needs', content: trimmed });
          } else if (trimmed.includes('表达') || trimmed.includes('语言')) {
            points.push({ type: 'communication', content: trimmed });
          } else if (trimmed.includes('引导') || trimmed.includes('推荐')) {
            points.push({ type: 'guidance', content: trimmed });
          } else if (trimmed.includes('记录') || trimmed.includes('信息')) {
            points.push({ type: 'record_keeping', content: trimmed });
          } else if (trimmed.includes('响应') || trimmed.includes('速度')) {
            points.push({ type: 'response_speed', content: trimmed });
          } else if (trimmed.includes('反馈') || trimmed.includes('收集')) {
            points.push({ type: 'feedback', content: trimmed });
          } else {
            points.push({ type: 'general', content: trimmed });
          }
        }
      });

      console.log('[回答生成] 提取的关键要点:', points);
      return points;
    },

    // 生成售前相关回答
    generatePreSalesAnswer(keyPoints, isDetailRequest = false) {
      const presalesPoints = keyPoints.filter(p =>
        p.type === 'product_knowledge' ||
        p.type === 'customer_needs' ||
        p.type === 'communication' ||
        p.type === 'guidance'
      );

      let answer = `**售前注意事项${isDetailRequest ? '详细' : ''}总结**\n\n`;
      answer += `根据知识库文档，售前工作的核心注意事项包括：\n\n`;

      if (presalesPoints.some(p => p.type === 'product_knowledge')) {
        answer += `**🎯 产品知识掌握**\n`;
        if (isDetailRequest) {
          answer += `- **全面掌握产品信息**：包括产品名称、规格型号、功能特点、技术参数、价格体系、优惠活动、适用场景等\n`;
          answer += `- **深入理解产品原理**：对于复杂产品，要能清晰解释其工作原理和与同类产品的差异\n`;
          answer += `- **熟悉应用场景**：了解产品在不同行业、不同规模客户中的应用案例\n`;
          answer += `- **掌握竞争优势**：清楚产品相比竞品的独特优势和卖点\n\n`;
        } else {
          answer += `- 全面掌握产品信息：名称、规格、功能、技术参数、价格等\n`;
          answer += `- 深入理解产品工作原理和竞争优势\n`;
          answer += `- 熟悉适用场景和优惠活动\n\n`;
        }
      }

      if (presalesPoints.some(p => p.type === 'customer_needs')) {
        answer += `**🤝 客户需求挖掘**\n`;
        if (isDetailRequest) {
          answer += `- **了解客户基本情况**：通过提问了解客户身份、所在行业、公司规模、决策流程等\n`;
          answer += `- **挖掘购买动机**：深入了解客户为什么要购买、解决什么问题、期望达到什么效果\n`;
          answer += `- **明确使用场景**：具体了解产品将在什么环境下使用、使用频率、使用人员等\n`;
          answer += `- **确定预算范围**：了解客户的预算区间，避免推荐超出预算的方案\n`;
          answer += `- **识别特殊要求**：发现客户对产品功能、服务、交付等方面的特殊需求\n\n`;
        } else {
          answer += `- 主动沟通了解客户基本情况（身份、行业、规模）\n`;
          answer += `- 深入了解购买目的、使用场景、预算范围\n`;
          answer += `- 精准定位客户的特殊要求\n\n`;
        }
      }

      if (presalesPoints.some(p => p.type === 'communication')) {
        answer += `**💬 沟通表达技巧**\n`;
        if (isDetailRequest) {
          answer += `- **语言表达**：使用简洁、明了、通俗易懂的语言，避免过多专业术语\n`;
          answer += `- **态度把控**：保持友好、热情、有耐心的服务态度，让客户感受到被重视\n`;
          answer += `- **积极回应**：及时回应客户的疑问和关切，不让客户感到被忽视\n`;
          answer += `- **倾听技巧**：认真倾听客户的表达，抓住关键信息和潜在需求\n\n`;
        } else {
          answer += `- 使用简洁、通俗易懂的语言\n`;
          answer += `- 保持友好、热情、耐心的态度\n`;
          answer += `- 避免专业术语和生硬表述\n\n`;
        }
      }

      if (presalesPoints.some(p => p.type === 'guidance')) {
        answer += `**🎯 引导购买技巧**\n`;
        if (isDetailRequest) {
          answer += `- **精准推荐**：根据客户需求和产品特点，推荐最合适的产品组合\n`;
          answer += `- **说明理由**：详细说明推荐理由，让客户理解为什么这个方案适合他们\n`;
          answer += `- **价值展示**：适时介绍产品的优惠活动和增值服务，展示额外价值\n`;
          answer += `- **决策支持**：在客户犹豫时，主动提供更多信息帮助客户决策\n`;
          answer += `- **避免强迫**：不得强迫或诱导客户购买，保持专业和诚信\n\n`;
        } else {
          answer += `- 根据需求推荐合适产品并说明理由\n`;
          answer += `- 适时介绍优惠活动和增值服务\n`;
          answer += `- 提供决策信息，避免强迫购买\n\n`;
        }
      }

      if (isDetailRequest) {
        answer += `💡 **关键提醒**：售前阶段是整个销售过程的基础，需要通过专业的产品知识、细致的需求挖掘、良好的沟通技巧和恰当的引导方式，建立与客户的信任关系，为后续的成交和长期合作奠定坚实基础。记住，急于求成往往适得其反，耐心和专业才是成功的关键。`;
      } else {
        answer += `💡 **关键提醒**：售前阶段的核心是建立信任、了解需求、提供价值，为后续成交奠定基础。`;
      }

      return answer;
    },

    // 生成售后相关回答
    generatePostSalesAnswer(keyPoints) {
      const postSalesPoints = keyPoints.filter(p =>
        p.type === 'feedback' ||
        p.type === 'record_keeping' ||
        p.type === 'response_speed'
      );

      let answer = `**售后注意事项总结**\n\n`;
      answer += `根据知识库文档，售后服务的关键注意事项包括：\n\n`;

      if (postSalesPoints.some(p => p.type === 'feedback')) {
        answer += `**📝 客户反馈管理**\n`;
        answer += `- 主动收集客户意见、建议和投诉\n`;
        answer += `- 认真记录并及时反馈给相关部门\n`;
        answer += `- 积极采纳合理建议并持续改进\n`;
        answer += `- 及时处理投诉并反馈处理结果\n\n`;
      }

      if (postSalesPoints.some(p => p.type === 'record_keeping')) {
        answer += `**📊 信息记录维护**\n`;
        answer += `- 及时更新客户售后信息\n`;
        answer += `- 完整记录维修、退换货记录\n`;
        answer += `- 确保客户信息的完整性和准确性\n\n`;
      }

      if (postSalesPoints.some(p => p.type === 'response_speed')) {
        answer += `**⚡ 响应速度要求**\n`;
        answer += `- 在规定时间内（如30秒内）响应客户\n`;
        answer += `- 避免让客户长时间等待\n`;
        answer += `- 建立快速响应机制\n\n`;
      }

      answer += `💡 **关键提醒**：售后服务直接影响客户满意度和品牌形象，需要建立完善的服务体系和快速响应机制。`;

      return answer;
    },

    // 生成通用销售回答
    generateGeneralSalesAnswer(keyPoints) {
      let answer = `**销售注意事项总结**\n\n`;
      answer += `根据知识库文档，销售工作的核心要点包括：\n\n`;

      // 按类型组织要点
      const categories = {
        'product_knowledge': '产品知识',
        'customer_needs': '客户需求',
        'communication': '沟通技巧',
        'guidance': '引导技巧',
        'record_keeping': '信息管理',
        'response_speed': '响应效率',
        'feedback': '反馈处理'
      };

      Object.entries(categories).forEach(([type, title]) => {
        const relevantPoints = keyPoints.filter(p => p.type === type);
        if (relevantPoints.length > 0) {
          answer += `**${title}**\n`;
          relevantPoints.forEach(point => {
            const summary = this.summarizePoint(point.content);
            answer += `- ${summary}\n`;
          });
          answer += `\n`;
        }
      });

      answer += `💡 **总结**：成功的销售需要专业知识、沟通技巧和优质服务的完美结合。`;

      return answer;
    },

    // 总结要点
    summarizePoint(content) {
      // 简化和总结长句子
      if (content.length > 50) {
        // 提取关键信息
        const keywords = content.match(/[\u4e00-\u9fa5]{2,4}/g) || [];
        const importantKeywords = keywords.filter(word =>
          !['需要', '包括', '通过', '进行', '实现', '提供', '确保'].includes(word)
        ).slice(0, 3);

        if (importantKeywords.length > 0) {
          return `${importantKeywords.join('、')}等关键要素`;
        }
      }

      return content.length > 30 ? content.substring(0, 30) + '...' : content;
    },

    // 格式化文档内容
    formatDocumentContent(content) {
      // 简单的段落划分，保持原文结构
      return content
        .replace(/([。！？])\s*/g, '$1\n\n')  // 在句号后添加段落分隔
        .replace(/(\d+\.)/g, '\n**$1**')      // 数字序号加粗
        .trim();
    },

    // 发送普通消息（无检索）
    async sendNormalMessage(userInput) {
      const messages = this.messages
        .filter(m => m.role === 'user' || m.role === 'assistant')
        .map(m => ({ role: m.role, content: m.content }));
      messages.push({ role: 'user', content: userInput });

      // 🔥 注释掉OpenAI兼容API调用
      // const response = await agentCompletionOpenAI(this.agentId, {
      //   model: 'deepseek-v3',
      //   messages,
      //   stream: false
      // }, false);
      // return await this.handleApiResponse(response);

      throw new Error('OpenAI兼容API已禁用，请使用原生Agent API');
    },

    // 检查工作流是否包含Answer组件
    checkForAnswerComponent() {
      try {
        // 通过父组件获取图表数据
        const graphData = this.$parent.$refs.x6Graph?.getGraphData();
        if (!graphData || !graphData.nodes) {
          console.log('[聊天API调试] 无法获取图表数据');
          return false;
        }

        // 检查是否有Answer类型的节点
        const hasAnswer = graphData.nodes.some(node => {
          const nodeType = node.type || node.data?.type || node.data?.modelType;
          return nodeType === 'answerNode' ||
                 nodeType === 'answer' ||
                 nodeType === 'Answer' ||
                 (node.data && (
                   node.data.label === 'Answer' ||
                   node.data.name === 'Answer' ||
                   node.data.modelName === 'Answer'
                 ));
        });

        console.log('[聊天API调试] Answer组件检查结果:', {
          hasAnswer,
          totalNodes: graphData.nodes.length,
          nodeTypes: graphData.nodes.map(n => n.type || n.data?.type || n.data?.modelType)
        });

        return hasAnswer;
      } catch (error) {
        console.error('[聊天API调试] 检查Answer组件时出错:', error);
        return false;
      }
    },

    handleApiError(error) {
      console.log('[聊天API调试] 处理API错误:', error);

      let errorContent = '抱歉，发生了错误：';

      // 根据错误类型提供更具体的错误信息
      if (error.message.includes('Agent ID未设置') || error.message.includes('未保存')) {
        errorContent = '请先保存当前工作流为Agent，然后再进行对话测试。';
      } else if (error.message.includes('Answer组件')) {
        errorContent = '工作流中缺少Answer组件。请从左侧组件面板拖拽一个Answer组件到画布上，然后保存工作流后再进行对话测试。';
      } else if (error.message.includes('There have to be an \'Answer\' component')) {
        errorContent = '系统检测到工作流中缺少必需的Answer组件。请添加Answer组件后重新保存工作流。';
      } else if (error.message.includes('API返回数据格式错误')) {
        errorContent = 'Agent响应格式异常，请检查Agent配置是否正确。';
      } else if (error.message.includes('timeout') || error.code === 'ECONNABORTED') {
        errorContent = 'Agent响应超时，可能是因为处理时间较长。请稍后重试或简化您的问题。';
      } else if (error.response && error.response.status === 404) {
        errorContent = 'Agent不存在，请确认Agent ID是否正确。';
      } else if (error.response && error.response.status === 401) {
        errorContent = 'API认证失败，请检查API密钥配置。';
      } else if (error.message.includes('网络')) {
        errorContent = '网络连接失败，请检查网络连接后重试。';
      } else {
        errorContent = `${error.message || '未知错误，请稍后重试'}`;
      }

      const errorMessage = {
        role: 'assistant',
        content: errorContent,
        timestamp: new Date()
      };
      this.messages.push(errorMessage);
    },
    
    clearChat() {
      this.$confirm('确定要清空所有对话记录吗？', '确认清空', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 重新初始化聊天
        this.initializeChat();
      }).catch(() => {
        // 用户取消
      });
    },
    
    scrollToBottom() {
      const container = this.$refs.messagesContainer;
      if (container) {
        container.scrollTop = container.scrollHeight;
      }
    },
    
    formatTime(timestamp) {
      return new Date(timestamp).toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      });
    },

    // 简单的Markdown渲染器
    renderMarkdown(content) {
      if (!content) return '';

      return content
        // 处理加粗文本 **text** -> <strong>text</strong>
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        // 处理换行符 -> <br>
        .replace(/\n/g, '<br>')
        // 处理段落分隔（双换行）-> <br><br>
        .replace(/<br><br>/g, '<br><br>');
    },

    // 保存对话数据到工作流状态
    saveConversationData() {
      try {
        this.saveStatus = 'saving';
        console.log('[对话保存] 开始保存对话数据');

        // 获取当前工作流数据
        const graphData = this.$parent.$refs.x6Graph?.getGraphData();
        if (!graphData) {
          console.warn('[对话保存] 无法获取工作流数据');
          this.saveStatus = 'error';
          return;
        }

        // 构建对话历史数据
        const conversationData = this.buildConversationData();

        // 更新工作流的对话相关数据
        this.updateWorkflowConversationData(graphData, conversationData);

        // 触发工作流保存
        this.$emit('conversation-updated', conversationData);

        // 更新保存状态
        this.saveStatus = 'saved';
        this.lastSaveTime = new Date().toISOString();

        console.log('[对话保存] 对话数据保存完成');

        // 显示保存成功提示（可选）
        this.$message({
          message: '对话数据已保存',
          type: 'success',
          duration: 1000,
          showClose: false
        });

      } catch (error) {
        console.error('[对话保存] 保存对话数据时出错:', error);
        this.saveStatus = 'error';

        this.$message({
          message: '保存对话数据失败',
          type: 'error',
          duration: 2000
        });
      }
    },

    // 构建对话数据
    buildConversationData() {
      console.log('[对话保存] 构建对话数据');

      // 构建history格式（简化版）
      const history = this.messages.map(msg => [
        msg.role,
        msg.content
      ]);

      // 构建messages格式（完整版）
      const messages = this.messages.map(msg => ({
        content: msg.content,
        id: msg.id || this.generateMessageId(),
        role: msg.role,
        timestamp: msg.timestamp || new Date().toISOString()
      }));

      // 构建执行路径
      const path = this.buildExecutionPath();

      // 构建引用信息
      const reference = this.buildReferenceData();

      const conversationData = {
        history,
        messages,
        path,
        reference,
        rerank: false,
        embed_id: "text-embedding-v2@Tongyi-Qianwen"
      };

      console.log('[对话保存] 构建的对话数据:', conversationData);
      return conversationData;
    },

    // 构建执行路径
    buildExecutionPath() {
      const path = [];

      // 添加初始路径
      if (this.messages.length > 0) {
        path.push(["begin"]);
      }

      // 为每个用户消息添加执行路径
      this.messages.forEach((msg, index) => {
        if (msg.role === 'user' && index > 0) {
          // 构建标准的执行路径
          const executionPath = this.buildStandardExecutionPath(msg);
          if (executionPath.length > 0) {
            path.push(executionPath);
          }
        }
      });

      return path;
    },

    // 构建标准执行路径
    buildStandardExecutionPath(userMessage) {
      // 检查是否为追问
      const isFollowUp = this.isFollowUpQuestion(userMessage.content);

      if (isFollowUp) {
        // 追问的完整路径
        return [
          "RewriteQuestion_0",
          "KeywordExtract_0",
          "Categorize_0",
          this.determineRetrievalPath(userMessage.content),
          this.determineGeneratePath(userMessage.content),
          "Answer_0"
        ];
      } else {
        // 新问题的路径
        return [
          "KeywordExtract_0",
          "Categorize_0",
          this.determineRetrievalPath(userMessage.content),
          this.determineGeneratePath(userMessage.content),
          "Answer_0"
        ];
      }
    },

    // 确定检索路径
    determineRetrievalPath(content) {
      // 简单的分类逻辑
      if (content.includes('售前') || content.includes('之前')) {
        return "Retrieval_0";
      } else if (content.includes('售后') || content.includes('之后')) {
        return "Retrieval_1";
      }
      return "Retrieval_0"; // 默认
    },

    // 确定生成路径
    determineGeneratePath(content) {
      // 对应检索路径的生成路径
      if (content.includes('售前') || content.includes('之前')) {
        return "Generate_0";
      } else if (content.includes('售后') || content.includes('之后')) {
        return "Generate_1";
      }
      return "Generate_0"; // 默认
    },

    // 保存检索结果
    saveRetrievalResults(chunks, keywords) {
      console.log('[检索结果保存] 保存检索结果:', chunks.length, '个片段');

      if (!chunks || chunks.length === 0) {
        return;
      }

      // 构建RAGFlow标准的reference格式
      const referenceData = this.buildReferenceFromChunks(chunks);

      // 保存到当前引用数据
      this.currentReference = referenceData;

      // 添加到检索结果历史
      this.retrievalResults.push({
        timestamp: new Date().toISOString(),
        keywords: keywords,
        chunks: chunks,
        reference: referenceData
      });

      console.log('[检索结果保存] 当前引用数据:', this.currentReference);
    },

    // 从chunks构建RAGFlow标准的reference格式
    buildReferenceFromChunks(chunks) {
      console.log('[引用构建] 开始构建引用数据');

      // 按文档分组chunks
      const docGroups = {};

      chunks.forEach(chunk => {
        const docId = chunk.document_id || chunk.doc_id;
        if (!docGroups[docId]) {
          docGroups[docId] = {
            chunks: [],
            doc_name: chunk.document_name || chunk.doc_name || 'Unknown Document'
          };
        }
        docGroups[docId].chunks.push(chunk);
      });

      // 构建reference数组
      const reference = Object.entries(docGroups).map(([docId, group]) => ({
        chunks: group.chunks.map(chunk => ({
          content: chunk.content || chunk.text || '',
          dataset_id: chunk.dataset_id || '',
          doc_type: chunk.doc_type || '',
          document_id: chunk.document_id || chunk.doc_id || docId,
          document_name: chunk.document_name || chunk.doc_name || group.doc_name,
          id: chunk.id || chunk.chunk_id || '',
          image_id: chunk.image_id || '',
          positions: chunk.positions || [],
          similarity: chunk.similarity || 0,
          term_similarity: chunk.term_similarity || 0,
          url: chunk.url || null,
          vector_similarity: chunk.vector_similarity || 0
        })),
        doc_aggs: [{
          doc_id: docId,
          doc_name: group.doc_name
        }]
      }));

      console.log('[引用构建] 构建完成，共', reference.length, '个文档组');
      return reference;
    },

    // 构建引用数据
    buildReferenceData() {
      // 返回当前对话轮次的引用数据
      console.log('[引用数据] 获取当前引用数据:', this.currentReference.length, '个引用');
      return this.currentReference || [];
    },

    // 清空当前引用数据（开始新的对话轮次时调用）
    clearCurrentReference() {
      console.log('[引用数据] 清空当前引用数据');
      this.currentReference = [];
    },

    // 更新工作流的对话数据
    updateWorkflowConversationData(graphData, conversationData) {
      console.log('[对话保存] 更新工作流对话数据');

      // 更新工作流的全局数据
      if (!graphData.conversationData) {
        graphData.conversationData = {};
      }

      // 保存对话数据
      graphData.conversationData = {
        ...graphData.conversationData,
        ...conversationData,
        lastUpdated: new Date().toISOString()
      };

      // 更新组件的输出数据
      this.updateComponentOutputs(graphData, conversationData);
    },

    // 更新组件输出数据
    updateComponentOutputs(graphData, conversationData) {
      if (!graphData.nodes) return;

      graphData.nodes.forEach(node => {
        if (!node.data) return;

        // 更新Answer组件的输出
        if (node.data.label === 'Answer' || node.id === 'Answer_0') {
          if (!node.data.output) {
            node.data.output = {};
          }

          // 保存最新的对话内容和引用数据
          const lastMessage = this.messages[this.messages.length - 1];
          if (lastMessage && lastMessage.role === 'assistant') {
            node.data.output.content = lastMessage.content;
            node.data.output.reference = conversationData.reference || [];

            // 如果有引用数据，也保存到组件的inputs中
            if (conversationData.reference && conversationData.reference.length > 0) {
              if (!node.data.inputs) {
                node.data.inputs = [];
              }
              // 更新或添加引用输入
              const referenceInput = {
                component_id: 'Retrieval',
                content: conversationData.reference,
                type: 'reference'
              };
              node.data.inputs = node.data.inputs.filter(input => input.type !== 'reference');
              node.data.inputs.push(referenceInput);
            }
          }
        }

        // 更新KeywordExtract组件的输出
        if (node.data.label === 'KeywordExtract' || node.id === 'KeywordExtract_0') {
          if (!node.data.output) {
            node.data.output = {};
          }

          // 保存最新提取的关键词
          const lastUserMessage = this.messages.filter(m => m.role === 'user').pop();
          if (lastUserMessage) {
            const keywords = extractKeywordsUtil(lastUserMessage.content, 3);
            node.data.output.content = {
              "0": keywords
            };
            node.data.output.component_id = {
              "0": node.id
            };
          }
        }
      });
    },

    // 生成消息ID
    generateMessageId() {
      return Math.random().toString(36).substring(2, 11) + Date.now().toString(36);
    },

    // 获取保存的对话数据
    getSavedConversationData() {
      try {
        const graphData = this.$parent.$refs.x6Graph?.getGraphData();
        if (graphData && graphData.conversationData) {
          console.log('[对话保存] 获取保存的对话数据:', graphData.conversationData);
          return graphData.conversationData;
        }
        return null;
      } catch (error) {
        console.error('[对话保存] 获取保存数据时出错:', error);
        return null;
      }
    },

    // 导出对话数据（用于调试）
    exportConversationData() {
      const conversationData = this.buildConversationData();
      const dataStr = JSON.stringify(conversationData, null, 2);

      // 创建下载链接
      const blob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `conversation_${new Date().getTime()}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      console.log('[对话保存] 对话数据已导出');
    },

    // 在控制台显示对话数据（用于调试）
    logConversationData() {
      const conversationData = this.buildConversationData();
      console.log('=== 当前对话数据 ===');
      console.log('History:', conversationData.history);
      console.log('Messages:', conversationData.messages);
      console.log('Path:', conversationData.path);
      console.log('Reference:', conversationData.reference);
      console.log('=== 检索结果历史 ===');
      console.log('检索次数:', this.retrievalResults.length);
      this.retrievalResults.forEach((result, index) => {
        console.log(`检索 ${index + 1}:`, {
          时间: result.timestamp,
          关键词: result.keywords,
          文档数: result.chunks.length,
          引用数: result.reference.length
        });
      });
      console.log('=== 当前引用数据 ===');
      console.log('当前引用:', this.currentReference);
      console.log('=== 完整数据 ===');
      console.log(JSON.stringify(conversationData, null, 2));
    },

    // 显示检索结果详情
    logRetrievalDetails() {
      console.log('=== 检索结果详情 ===');
      if (this.retrievalResults.length === 0) {
        console.log('暂无检索结果');
        return;
      }

      this.retrievalResults.forEach((result, index) => {
        console.log(`\n--- 检索 ${index + 1} ---`);
        console.log('时间:', result.timestamp);
        console.log('关键词:', result.keywords);
        console.log('检索到的文档片段:');

        result.chunks.forEach((chunk, chunkIndex) => {
          console.log(`  片段 ${chunkIndex + 1}:`);
          console.log(`    文档: ${chunk.document_name || chunk.doc_name}`);
          console.log(`    相似度: ${chunk.similarity || 0}`);
          console.log(`    内容: ${(chunk.content || chunk.text || '').substring(0, 100)}...`);
        });

        console.log('构建的引用数据:');
        result.reference.forEach((ref, refIndex) => {
          console.log(`  引用 ${refIndex + 1}:`);
          console.log(`    文档组: ${ref.doc_aggs?.[0]?.doc_name}`);
          console.log(`    片段数: ${ref.chunks?.length || 0}`);
        });
      });
    },

    // 获取当前处理模式描述
    getCurrentProcessingMode() {
      const hasRetrievalComponent = this.checkForRetrievalComponent();

      if (!hasRetrievalComponent) {
        return '普通模式 - 直接通过Agent工作流处理';
      }

      if (this.alwaysUseWorkflow) {
        return 'Agent工作流模式 - 包含检索组件，通过完整工作流处理';
      } else {
        return '混合检索模式 - 可能绕过工作流直接处理';
      }
    }
  }
};
</script>

<style scoped>
.chat-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background-color: #f8f9fa;
}

.empty-chat {
  text-align: center;
  color: #909399;
  margin-top: 100px;
}

.empty-chat i {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
}

.message-item {
  display: flex;
  margin-bottom: 20px;
  animation: fadeIn 0.3s ease-in;
}

.user-message {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: white;
  flex-shrink: 0;
}

.user-message .message-avatar {
  background-color: #409EFF;
  margin-left: 12px;
}

.ai-message .message-avatar {
  background-color: #67C23A;
  margin-right: 12px;
}

.message-content {
  max-width: 70%;
  min-width: 100px;
}

.message-text {
  background-color: white;
  padding: 12px 16px;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  line-height: 1.5;
  word-wrap: break-word;
}

.user-message .message-text {
  background-color: #409EFF;
  color: white;
}

.message-time {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  text-align: right;
}

.user-message .message-time {
  text-align: left;
}

/* 模拟流式输出光标样式 */
.streaming-cursor {
  display: inline-block;
  color: #409EFF;
  font-weight: bold;
  animation: blink 1s infinite;
  margin-left: 2px;
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

.typing-indicator {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #909399;
  margin-right: 4px;
  animation: typing 1.4s infinite;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
  margin-right: 0;
}

.chat-config-area {
  border-top: 1px solid #EBEEF5;
  border-bottom: 1px solid #EBEEF5;
  padding: 15px 20px;
  background-color: #f8f9fa;
}

.chat-input-area {
  padding: 20px;
  background-color: white;
}

.input-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.input-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10px);
  }
}

/* 自定义滚动条 */
.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>