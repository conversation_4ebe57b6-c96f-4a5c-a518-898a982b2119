// AGENT MANAGEMENT API 封装
// 这里将封装 agent 的创建、查询、推理等接口
import ragflowRequest, { RAGFLOW_API_KEY } from './index';

/**
 * 获取Agent列表
 * @param {Object} params - 查询参数
 * @param {number} [params.page=1] - 页码，默认为1
 * @param {number} [params.page_size=30] - 每页数量，默认为30
 * @param {string} [params.orderby='create_time'] - 排序字段，可选值：create_time, update_time
 * @param {boolean} [params.desc=true] - 是否降序排列，默认为true
 * @param {string} [params.name] - 按名称过滤
 * @param {string} [params.id] - 按ID过滤
 * @returns {Promise}
 */
export function listAgents(params = {}) {
  const {
    page = 1,
    page_size = 30,
    orderby = 'create_time',
    desc = true,
    name,
    id
  } = params;

  const queryParams = new URLSearchParams();
  
  // 添加查询参数
  queryParams.append('page', page.toString());
  queryParams.append('page_size', page_size.toString());
  queryParams.append('orderby', orderby);
  queryParams.append('desc', desc.toString());
  
  if (name) {
    queryParams.append('name', name);
  }
  
  if (id) {
    queryParams.append('id', id);
  }

  return ragflowRequest.get(
    `/api/v1/agents?${queryParams.toString()}`,
    {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${RAGFLOW_API_KEY}`
      }
    }
  );
}

/**
 * 创建Agent
 * @param {Object} data - Agent数据
 * @param {string} data.title - Agent标题（必需）
 * @param {string} [data.description] - Agent描述
 * @param {Object} data.dsl - Canvas DSL对象（必需）
 * @returns {Promise}
 */
export function createAgent(data) {
  return ragflowRequest.post(
    '/api/v1/agents',
    data,
    {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${RAGFLOW_API_KEY}`
      }
    }
  );
}

/**
 * 更新Agent
 * @param {string} agentId - Agent ID
 * @param {Object} data - 更新的数据
 * @param {string} [data.title] - Agent标题
 * @param {string} [data.description] - Agent描述
 * @param {Object} [data.dsl] - Canvas DSL对象
 * @returns {Promise}
 */
export function updateAgent(agentId, data) {
  return ragflowRequest.put(
    `/api/v1/agents/${agentId}`,
    data,
    {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${RAGFLOW_API_KEY}`
      }
    }
  );
}

/**
 * 删除Agent
 * @param {string} agentId - Agent ID
 * @returns {Promise}
 */
export function deleteAgent(agentId) {
  return ragflowRequest.delete(
    `/api/v1/agents/${agentId}`,
    {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${RAGFLOW_API_KEY}`
      }
    }
  );
} 

/**
 * 创建 agent 会话
 * @param {string} agentId - agent id
 * @param {Object|FormData} data - 请求体参数（json对象或FormData）
 * @param {Object} options - 其他选项，如 { user_id, isFormData }
 * @returns {Promise}
 */
export function createAgentSession(agentId, data, options = {}) {
  const { user_id, isFormData } = options;
  let url = `/api/v1/agents/${agentId}/sessions`;
  if (user_id) {
    url += `?user_id=${encodeURIComponent(user_id)}`;
  }
  return ragflowRequest.post(
    url,
    data,
    {
      headers: {
        'Authorization': `Bearer ${RAGFLOW_API_KEY}`,
        'Content-Type': isFormData ? 'multipart/form-data' : 'application/json'
      }
    }
  );
} 

/**
 * 与 agent 对话
 * @param {string} agentId - agent id
 * @param {Object} data - { question, stream, session_id, user_id, sync_dsl, ...otherParams }
 * @returns {Promise}
 */
export function converseWithAgent(agentId, data) {
  return ragflowRequest.post(
    `/api/v1/agents/${agentId}/completions`,
    data,
    {
      headers: {
        'Authorization': `Bearer ${RAGFLOW_API_KEY}`,
        'Content-Type': 'application/json'
      }
    }
  );
} 

/**
 * 获取 agent 的会话列表
 * @param {string} agentId - agent id
 * @param {Object} params - 查询参数，如 { page, page_size, orderby, desc, id, user_id, dsl }
 * @returns {Promise}
 */
export function listAgentSessions(agentId, params = {}) {
  return ragflowRequest.get(
    `/api/v1/agents/${agentId}/sessions`,
    {
      params,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${RAGFLOW_API_KEY}`
      }
    }
  );
} 

/**
 * 删除 agent 的会话
 * @param {string} agentId - agent id
 * @param {Array<string>} ids - 要删除的 session id 列表（可选，空则删除全部）
 * @returns {Promise}
 */
export function deleteAgentSessions(agentId, ids = []) {
  return ragflowRequest.delete(
    `/api/v1/agents/${agentId}/sessions`,
    {
      data: { ids },
      headers: {
        'Authorization': `Bearer ${RAGFLOW_API_KEY}`,
        'Content-Type': 'application/json'
      }
    }
  );
} 

