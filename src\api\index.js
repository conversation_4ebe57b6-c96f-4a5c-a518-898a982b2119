// src/api/index.js
import axios from 'axios';

// 开发环境使用代理，生产环境使用实际地址
export const RAGFLOW_BASE_URL = 'http://140.143.132.222:8000'; // 生产环境使用实际地址
export const RAGFLOW_API_KEY = 'ragflow-c5ZDA5MmJhNjJlNjExZjA5ZWE4NzZjMD';

const ragflowRequest = axios.create({
  baseURL: RAGFLOW_BASE_URL,
  timeout: 20000,
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${RAGFLOW_API_KEY}`
  }
});

import { extractKeywords as extractKeywordsUtil } from '../utils/keywordExtractor.js';

// 对话历史管理
class ConversationManager {
  constructor() {
    this.conversations = new Map(); // sessionId -> conversation history
    this.storageKey = 'ragflow_conversations'; // localStorage key
    this.maxHistoryLength = 50; // 最大保存消息数量

    // 从localStorage加载已有对话
    this.loadFromStorage();
  }

  /**
   * 从localStorage加载对话历史
   */
  loadFromStorage() {
    try {
      const stored = localStorage.getItem(this.storageKey);
      if (stored) {
        const data = JSON.parse(stored);
        Object.entries(data).forEach(([sessionId, messages]) => {
          this.conversations.set(sessionId, messages);
        });
        console.log('[对话管理] 从本地存储加载对话历史:', Object.keys(data));
      }
    } catch (error) {
      console.error('[对话管理] 加载本地存储失败:', error);
    }
  }

  /**
   * 保存对话历史到localStorage
   */
  saveToStorage() {
    try {
      const data = {};
      this.conversations.forEach((messages, sessionId) => {
        // 只保存最近的消息，避免存储过大
        data[sessionId] = messages.slice(-this.maxHistoryLength);
      });
      localStorage.setItem(this.storageKey, JSON.stringify(data));
      console.log('[对话管理] 保存对话历史到本地存储');
    } catch (error) {
      console.error('[对话管理] 保存本地存储失败:', error);
    }
  }

  /**
   * 添加对话记录
   * @param {string} sessionId - 会话ID
   * @param {string} role - 角色 (USER/ASSISTANT)
   * @param {string} content - 内容
   */
  addMessage(sessionId, role, content) {
    if (!this.conversations.has(sessionId)) {
      this.conversations.set(sessionId, []);
    }

    const conversation = this.conversations.get(sessionId);
    conversation.push({
      role,
      content,
      timestamp: new Date().toISOString(),
      id: this.generateMessageId()
    });

    // 限制单个会话的消息数量
    if (conversation.length > this.maxHistoryLength) {
      conversation.splice(0, conversation.length - this.maxHistoryLength);
    }

    // 保存到localStorage
    this.saveToStorage();

    console.log(`[对话管理] 添加${role}消息到会话${sessionId}:`, content);
  }

  /**
   * 生成消息ID
   * @returns {string} 消息ID
   */
  generateMessageId() {
    return `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * 获取格式化的对话历史
   * @param {string} sessionId - 会话ID
   * @returns {string} 格式化的对话历史
   */
  getFormattedHistory(sessionId) {
    const conversation = this.conversations.get(sessionId) || [];

    return conversation.map(msg =>
      `${msg.role}:${msg.content}`
    ).join('\n');
  }

  /**
   * 获取最后N轮对话
   * @param {string} sessionId - 会话ID
   * @param {number} rounds - 轮数
   * @returns {string} 最近的对话历史
   */
  getRecentHistory(sessionId, rounds = 3) {
    const conversation = this.conversations.get(sessionId) || [];
    const recentMessages = conversation.slice(-rounds * 2); // 每轮包含USER和ASSISTANT

    return recentMessages.map(msg =>
      `${msg.role}:${msg.content}`
    ).join('\n');
  }

  /**
   * 清除会话历史
   * @param {string} sessionId - 会话ID
   */
  clearSession(sessionId) {
    this.conversations.delete(sessionId);
    console.log(`[对话管理] 清除会话${sessionId}的历史记录`);
  }
}

// 创建全局对话管理器实例
const conversationManager = new ConversationManager();

// 导出对话管理器相关功能
export { conversationManager };

/**
 * 添加用户消息到对话历史
 * @param {string} sessionId - 会话ID
 * @param {string} content - 用户输入内容
 */
export function addUserMessage(sessionId, content) {
  conversationManager.addMessage(sessionId, 'USER', content);
}

/**
 * 添加助手回复到对话历史
 * @param {string} sessionId - 会话ID
 * @param {string} content - 助手回复内容
 */
export function addAssistantMessage(sessionId, content) {
  conversationManager.addMessage(sessionId, 'ASSISTANT', content);
}

/**
 * 获取格式化的对话历史
 * @param {string} sessionId - 会话ID
 * @returns {string} 格式化的对话历史
 */
export function getConversationHistory(sessionId) {
  return conversationManager.getFormattedHistory(sessionId);
}

/**
 * 清除会话历史
 * @param {string} sessionId - 会话ID
 */
export function clearConversationHistory(sessionId) {
  conversationManager.clearSession(sessionId);
}

/**
 * 关键词提取API (增强版 - 支持对话历史)
 * @param {Object} params - 关键词提取参数
 * @param {string} params.text - 要提取关键词的文本
 * @param {string} [params.session_id] - 会话ID
 * @param {string} params.llm_id - 使用的LLM模型ID
 * @param {number} params.top_n - 提取的关键词数量
 * @param {number} params.temperature - 温度参数
 * @param {number} params.max_tokens - 最大token数
 * @param {boolean} [params.use_conversation_history] - 是否使用对话历史
 * @returns {Promise} API响应
 */
export async function extractKeywords(params) {
  console.log('[关键词提取API] 开始调用关键词提取接口');
  console.log('[关键词提取API] 请求参数:', params);

  try {
    const requestData = {
      text: params.text || '',
      llm_id: params.llm_id || 'qwen-max@Tongyi-Qianwen',
      top_n: params.top_n || 5,
      temperature: params.temperature || 0.1,
      max_tokens: params.max_tokens || 256,
      frequency_penalty: params.frequency_penalty || 0.7,
      presence_penalty: params.presence_penalty || 0.4,
      top_p: params.top_p || 0.3
    };

    // 处理对话历史
    let processText = requestData.text;
    if (params.use_conversation_history && params.session_id) {
      // 获取对话历史
      const conversationHistory = conversationManager.getFormattedHistory(params.session_id);
      if (conversationHistory) {
        processText = conversationHistory;
        console.log('[关键词提取API] 使用对话历史:', conversationHistory);
      }
    }

    console.log('[关键词提取API] 实际发送的请求数据:', requestData);
    console.log('[关键词提取API] 处理的文本内容:', processText);

    // 使用封装的关键词提取工具
    console.log('[关键词提取API] 使用智能关键词提取工具');
    const extractedKeywords = extractKeywordsUtil(processText, requestData.top_n);
    console.log('[关键词提取API] 智能提取结果:', extractedKeywords);

    const result = {
      success: true,
      keywords: extractedKeywords,
      component_id: params.component_id || '',
      session_id: params.session_id || '',
      conversation_context: params.use_conversation_history ? processText : null,
      raw_response: {
        method: 'intelligent_extraction',
        keywords: extractedKeywords,
        parameters: requestData,
        used_conversation_history: !!params.use_conversation_history
      }
    };

    console.log('[关键词提取API] 最终返回结果:', result);
    return result;

  } catch (error) {
    console.error('[关键词提取API] 调用失败:', error);
    console.error('[关键词提取API] 错误详情:', {
      message: error.message,
      response: error.response?.data,
      status: error.response?.status
    });

    // 返回错误结果，但保持结构一致
    return {
      success: false,
      keywords: '',
      component_id: params.component_id || '',
      error: error.message,
      raw_response: error.response?.data
    };
  }
}

export default ragflowRequest;