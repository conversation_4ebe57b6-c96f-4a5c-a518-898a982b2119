/**
 * X6 图表基本配置
 */

/**
 * 创建图表配置对象
 * @param {HTMLElement} container - 图表容器元素
 * @returns {Object} 图表配置对象
 */
export function createGraphConfig(container) {
  return {
    container,
    width: '100%',
    height: '100%',
    autoResize: true,
    // 设置网格背景
    grid: {
      size: 10,
      visible: true,
      type: 'dot',
      args: {
        color: '#cccccc',
        thickness: 1,
      },
    },
    // 启用画布拖动功能
    panning: {
      enabled: true,
      modifiers: null,
    },
    // 启用鼠标滚轮缩放
    mousewheel: {
      enabled: true,
      zoomAtMousePosition: true,
      modifiers: 'ctrl',
      minScale: 0.5,
      maxScale: 3,
    },
    // 添加自定义的节点交互处理
    interacting: {
      nodeMovable: (view) => {
        const node = view.cell;
        const nodeData = node.getData();
        
        // 如果是IterationItem节点，则禁止拖动
        if (nodeData && nodeData.isIterationItem) {
          return false;
        }
        
        return true;
      }
    },
    // 选择配置
    selecting: {
      enabled: true,
      multiple: true,
      rubberband: true,
      showNodeSelectionBox: false, // 禁用默认的选择框
      showEdgeSelectionBox: false,
      // 完全禁用选择时的虚线框
      strict: false,
      showNodeSelectionBoxes: false,
      showEdgeSelectionBoxes: false,
      selectNodeOnMoved: false,
      selectEdgeOnMoved: false,
      // 禁用默认的选择盒样式
      pointerEvents: 'none',
      content: '',
      createSelectionBox: () => null // 不创建选择框
    },
    // 高亮配置
    highlighting: {
      'default': {
        name: 'stroke',
        args: {
          padding: 4,
          attrs: {
            stroke: '#1890FF',
            strokeWidth: 2,
            strokeDasharray: 'none', // 确保没有虚线
          }
        }
      }
    }
  };
} 