<template>
  <div class="common-settings">
    <div class="form-section">
      <h4>基本设置</h4>
      <el-form label-position="top" label-width="100px" size="small">
        <el-form-item label="节点名称">
          <el-input v-model="nodeData.name"></el-input>
        </el-form-item>
        <el-form-item label="描述">
          <el-input type="textarea" v-model="nodeData.description" rows="3"></el-input>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CommonSettings',
  props: {
    initialData: {
      type: Object,
      default: () => ({
        name: '',
        description: ''
      })
    }
  },
  data() {
    return {
      nodeData: {
        name: '',
        description: ''
      }
    };
  },
  watch: {
    initialData: {
      handler(newVal) {
        this.nodeData = {
          name: newVal.name || '',
          description: newVal.description || ''
        };
      },
      immediate: true,
      deep: true
    },
    nodeData: {
      handler(newVal) {
        this.$emit('update', newVal);
      },
      deep: true
    }
  }
}
</script>

<style scoped>
.form-section {
  margin-bottom: 24px;
}

.form-section h4 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 16px;
  color: #333;
}
</style> 