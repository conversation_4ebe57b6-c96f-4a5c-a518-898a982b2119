<template>
  <div class="begin-settings">
    <!-- 设置开场白部分 -->
    <div class="setting-section">
      <div class="section-header">
        <span class="section-title">设置开场白</span>
        <el-tooltip content="设置AI助手的开场白，这将是用户开始对话时看到的第一条消息" placement="top">
          <i class="el-icon-question section-help-icon"></i>
        </el-tooltip>
      </div>
      <el-input
        v-model="localForm.prologue"
        type="textarea"
        :rows="4"
        placeholder="你好！我是你的助理，有什么可以帮到你的吗？"

        class="prologue-input"
      />
    </div>

    <!-- 输入部分 -->
    <div class="setting-section">
      <el-collapse v-model="activeCollapse">
        <el-collapse-item name="inputs">
          <template slot="title">
            <span class="collapse-title">输入</span>
          </template>

          <!-- 输入变量表格 -->
          <div class="inputs-table">
            <div class="table-header">
              <div class="table-cell">Key</div>
              <div class="table-cell">名称</div>
              <div class="table-cell">类型</div>
              <div class="table-cell">可选项</div>
              <div class="table-cell">操作</div>
            </div>

            <!-- 空状态 -->
            <div class="empty-state">
              <div class="empty-icon">
                <i class="el-icon-folder-opened"></i>
              </div>
              <div class="empty-text">暂无数据</div>
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>

    <!-- 保存按钮 -->
    <div class="save-section">
      <el-button type="primary" @click="handleSave" class="save-btn">保存设置</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BeginSettings',
  props: {
    nodeData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      localForm: {
        prologue: "Hi! I'm your smart assistant. What can I do for you?"
      },
      activeCollapse: ['inputs'] // 默认展开输入面板
    };
  },
  watch: {
    nodeData: {
      handler(newData) {
        if (newData) {
          // 优先从form中读取，然后从其他地方读取
          const prologue = (newData.form && newData.form.prologue) ||
                          (newData.output && newData.output.content && newData.output.content['0'] && newData.output.content['0'].content) ||
                          newData.prologue ||
                          "Hi! I'm your smart assistant. What can I do for you?";

          this.localForm = {
            prologue: prologue
          };
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    // 获取当前表单数据，供父组件在关闭时调用
    getFormData() {
      return {
        form: { ...this.localForm }
      };
    },

    // 处理保存按钮点击
    handleSave() {
      console.log('[BeginSettings] 用户点击保存按钮');
      this.$emit('save', {
        form: { ...this.localForm }
      });
    }
  }
};
</script>

<style scoped>
.begin-settings {
  padding: 16px;
}

.setting-section {
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.section-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-right: 8px;
}

.section-help-icon {
  color: #909399;
  font-size: 14px;
  cursor: pointer;
}

.section-help-icon:hover {
  color: #409EFF;
}

.prologue-input {
  width: 100%;
}

.prologue-input .el-textarea__inner {
  resize: vertical;
  min-height: 100px;
  border-radius: 6px;
  border: 1px solid #DCDFE6;
  font-size: 14px;
  line-height: 1.5;
}

.prologue-input .el-textarea__inner:focus {
  border-color: #409EFF;
}

/* 折叠面板样式 */
.collapse-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

/* 输入表格样式 */
.inputs-table {
  border: 1px solid #EBEEF5;
  border-radius: 6px;
  overflow: hidden;
}

.table-header {
  display: flex;
  background-color: #F5F7FA;
  border-bottom: 1px solid #EBEEF5;
}

.table-cell {
  flex: 1;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 500;
  color: #606266;
  border-right: 1px solid #EBEEF5;
}

.table-cell:last-child {
  border-right: none;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #909399;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-text {
  font-size: 14px;
  color: #C0C4CC;
}

/* 自定义折叠面板样式 */
.begin-settings ::v-deep .el-collapse {
  border: none;
}

.begin-settings ::v-deep .el-collapse-item__header {
  background-color: transparent;
  border: none;
  padding-left: 0;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.begin-settings ::v-deep .el-collapse-item__content {
  padding: 16px 0 0 0;
}

.begin-settings ::v-deep .el-collapse-item__wrap {
  border: none;
}

/* 保存按钮样式 */
.save-section {
  margin-top: 24px;
  text-align: right;
  padding-top: 16px;
  border-top: 1px solid #EBEEF5;
}

.save-btn {
  min-width: 100px;
}
</style>
