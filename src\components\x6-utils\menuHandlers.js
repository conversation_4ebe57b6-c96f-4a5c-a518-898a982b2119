/**
 * 节点菜单处理相关功能模块
 */

/**
 * 计算节点菜单位置
 * @param {Object} graph - 图表实例
 * @param {Object} node - 节点对象
 * @returns {Object} 菜单位置坐标 {top, left}
 */
export function calculateMenuPosition(graph, node) {
  if (!graph || !node) return { top: 0, left: 0 };
  
  // 获取节点的包围盒
  const bbox = node.getBBox();
  
  // 获取节点数据
  const nodeData = node.getData() || {};
  
  // 根据节点类型和位置计算菜单位置
  let menuX, menuY;
  
  // 判断是否是循环内部的节点
  const isInsideLoop = nodeData.parentLoopId || nodeData.isIterationItem;
  
  if (isInsideLoop) {
    // 对于循环内部的节点，将菜单放在节点上方
    const position = graph.localToClient(
      bbox.x + bbox.width / 2, // 水平居中
      bbox.y // 顶部
    );
    
    menuX = position.x - 50; // 水平居中偏移
    menuY = position.y - 40; // 在节点上方显示
  } else {
    // 对于普通节点，保持原来的右侧显示位置
    const position = graph.localToClient(
      bbox.x + bbox.width,
      bbox.y + (nodeData.modelType === 'optimization' || 
                nodeData.modelType === 'keywords' || 
                nodeData.modelType === 'generation' ? bbox.height * 0.35 : bbox.height * 0.5)
    );
    
    menuX = position.x - 240;
    menuY = position.y - 40;
  }
  
  return { top: menuY, left: menuX };
}

/**
 * 处理节点菜单鼠标进入事件
 * @param {Object} menuState - 菜单状态对象
 * @param {Function} clearTimeoutFn - 清除定时器的函数
 */
export function handleMenuMouseEnter(menuState, clearTimeoutFn) {
  menuState.isMouseOverMenu = true;
  // 取消关闭菜单的延时
  if (clearTimeoutFn) {
    clearTimeoutFn();
  }
}

/**
 * 处理节点菜单鼠标离开事件
 * @param {Object} menuState - 菜单状态对象
 * @param {Function} setTimeoutFn - 设置定时器的函数
 * @param {Function} hideMenuFn - 隐藏菜单的函数
 */
export function handleMenuMouseLeave(menuState, setTimeoutFn, hideMenuFn) {
  menuState.isMouseOverMenu = false;
  // 鼠标离开菜单，延时关闭
  setTimeoutFn(() => {
    hideMenuFn();
  }, 150);
}

/**
 * 检查节点是否是循环内部节点
 * @param {Object} nodeData - 节点数据
 * @returns {Boolean} 是否是循环内部节点
 */
export function isNodeInsideLoop(nodeData) {
  if (!nodeData) return false;
  return nodeData.parentLoopId || nodeData.isIterationItem;
}

/**
 * 检查节点是否是迭代项节点
 * @param {Object} nodeData - 节点数据
 * @returns {Boolean} 是否是迭代项节点
 */
export function isIterationItem(nodeData) {
  if (!nodeData) return false;
  return nodeData.isIterationItem === true;
}

/**
 * 复制节点
 * @param {Object} graph - 图表实例
 * @param {String} nodeId - 要复制的节点ID
 * @param {Function} createNodeFn - 创建节点的函数
 */
export function duplicateNode(graph, nodeId, createNodeFn) {
  if (!nodeId) return;
  
  const node = graph.getCellById(nodeId);
  if (!node) return;
  
  // 获取节点数据和位置
  const { x, y } = node.position();
  const nodeData = node.getData();
  
  // 创建新节点（偏移一点位置）
  if (nodeData) {
    const model = {
      id: nodeData.modelId,
      name: nodeData.modelName,
      type: nodeData.modelType,
      // 使用相同的样式
      bgColor: node.attr('icon-bg/fill'),
      iconColor: node.attr('icon/fill'),
    };
    
    // 创建新节点，位置稍微偏移
    createNodeFn(model, x + 20, y + 20);
  }
}

/**
 * 删除节点
 * @param {Object} graph - 图表实例
 * @param {String} nodeId - 要删除的节点ID
 * @param {Function} showMessageFn - 显示消息的函数
 */
export function deleteNode(graph, nodeId, showMessageFn) {
  if (!nodeId) return;
  
  // 避免删除开始节点
  if (nodeId === 'start-node') {
    if (showMessageFn) {
      showMessageFn({
        message: '不能删除开始节点',
        type: 'warning',
        duration: 2000
      });
    }
    return;
  }
  
  // 检查是否是循环节点，如果是，同时删除其内容区域和内部节点
  const node = graph.getCellById(nodeId);
  if (node) {
    const nodeData = node.getData();
    if (nodeData && nodeData.isLoop) {
      // 获取循环内容区域节点
      const contentId = `${nodeId}-content`;
      const contentNode = graph.getCellById(contentId);
      
      if (contentNode) {
        // 找出所有在循环内部的节点
        const allNodes = graph.getNodes();
        const nodesToRemove = [];
        
        allNodes.forEach(innerNode => {
          const innerNodeData = innerNode.getData();
          // 检查节点是否属于这个循环
          if (innerNodeData && innerNodeData.parentLoopId === nodeId) {
            nodesToRemove.push(innerNode.id);
          }
        });
        
        // 找出与这些节点相关的所有连接线
        const allEdges = graph.getEdges();
        const edgesToRemove = [];
        
        allEdges.forEach(edge => {
          const sourceId = edge.getSourceCellId();
          const targetId = edge.getTargetCellId();
          
          // 如果连接线连接了任何将被删除的节点，也将其删除
          if (nodesToRemove.includes(sourceId) || 
              nodesToRemove.includes(targetId) ||
              sourceId === nodeId || 
              targetId === nodeId ||
              sourceId === contentId || 
              targetId === contentId) {
            edgesToRemove.push(edge.id);
          }
        });
        
        // 先删除连接线
        edgesToRemove.forEach(edgeId => {
          graph.removeCell(edgeId);
        });
        
        // 然后删除内部节点
        nodesToRemove.forEach(innerNodeId => {
          graph.removeCell(innerNodeId);
        });
        
        // 最后删除内容区域节点
        graph.removeCell(contentId);
        
        if (showMessageFn && (nodesToRemove.length > 0 || edgesToRemove.length > 0)) {
          showMessageFn({
            message: `已删除循环及其内部的 ${nodesToRemove.length} 个节点和 ${edgesToRemove.length} 条连接线`,
            type: 'success',
            duration: 2000
          });
        }
        
        // 删除节点本身
        graph.removeCell(nodeId);
        return; // 已经删除了节点，不需要再执行后面的代码
      }
    }
  }
  
  // 对于非循环节点，删除与其相关的连接线
  const allEdges = graph.getEdges();
  allEdges.forEach(edge => {
    const sourceId = edge.getSourceCellId();
    const targetId = edge.getTargetCellId();
    
    if (sourceId === nodeId || targetId === nodeId) {
      graph.removeCell(edge.id);
    }
  });
  
  // 最后删除节点本身
  graph.removeCell(nodeId);
} 