/**
 * 连接验证相关功能模块
 */
import { Shape } from '@antv/x6';

/**
 * 创建边的工厂函数
 * @returns {Object} 新创建的边实例
 */
export function createEdge() {
  return new Shape.Edge({
    attrs: {
      line: {
        stroke: '#A8ABF7', // 浅紫色
        strokeWidth: 2,
        sourceMarker: null,
      },
    },
    connector: { 
      name: 'smooth',
      args: { radius: 30 } // 平滑曲线
    },
    router: {
      name: 'normal'
    },
    // 添加边工具
    tools: [
      {
        name: 'button-remove',
        args: {
          distance: 0.5,
          size: 14,
          markup: [
            {
              tagName: 'circle',
              selector: 'button',
              attrs: {
                r: 7,
                fill: '#AAAAAA',
                stroke: '#FFFFFF',
                strokeWidth: 1,
                cursor: 'pointer',
              }
            },
            {
              tagName: 'path',
              selector: 'icon',
              attrs: {
                d: 'M -3 -3 3 3 M -3 3 3 -3',
                fill: 'none',
                stroke: '#FFFFFF',
                strokeWidth: 1.5,
                pointerEvents: 'none'
              }
            }
          ]
        }
      },
    ],
  });
}

/**
 * 连接验证函数
 * @param {Object} params - 连接参数
 * @returns {Boolean} 是否允许连接
 */
export function validateConnection({ sourceView, targetView, sourceMagnet, targetMagnet, sourceCell, targetCell }) {
  if (sourceView === targetView) return false;
  if (!sourceMagnet || !targetMagnet) return false;
  
  // 获取源节点和目标节点的数据
  const sourceData = sourceCell.getData();
  const targetData = targetCell.getData();
  
  // 检查是否是循环内容区域的连接
  if (sourceData && sourceData.isLoopContent) {
    // 检查目标节点是否是循环内部的节点
    if (targetData && targetData.parentLoopId === sourceData.parentId) {
      return false; // 不允许循环内容区域连接到其内部节点
    }
    return true; // 允许连接到外部节点
  }
  
  if (targetData && targetData.isLoopContent) {
    // 检查源节点是否是循环内部的节点
    if (sourceData && sourceData.parentLoopId === targetData.parentId) {
      return false; // 不允许内部节点连接到循环内容区域
    }
    return true; // 允许外部节点连接到循环内容区域
  }
  
  // 检查是否是循环节点
  if (sourceData && sourceData.isLoop) {
    // 循环节点本身不能连接到任何节点
    return false;
  }
  
  if (targetData && targetData.isLoop) {
    // 任何节点都不能连接到循环节点本身
    return false;
  }
  
  return true;
}

/**
 * 获取连接配置
 * @returns {Object} 连接配置对象
 */
export function getConnectingConfig() {
  return {
    router: 'manhattan',
    connector: {
      name: 'rounded',
      args: { radius: 8 },
    },
    anchor: 'center',
    connectionPoint: 'anchor',
    allowBlank: false,
    snap: { radius: 20 },
    createEdge,
    validateConnection,
  };
} 