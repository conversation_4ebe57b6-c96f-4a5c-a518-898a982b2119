// FILE MANAGEMENT WITHIN DATASET API 封装
// 这里将封装数据集内文件的上传、删除、查询等接口
import ragflowRequest, { RAGFLOW_API_KEY } from './index';


/**
 * 上传文档到数据集
 * @param {string} datasetId - 数据集ID
 * @param {File|File[]|Blob|Blob[]} files - 单个或多个文件对象
 * @returns {Promise}
 */
export function uploadDatasetDocuments(datasetId, files) {
    const formData = new FormData();
    if (Array.isArray(files)) {
      files.forEach(file => formData.append('file', file));
    } else {
      formData.append('file', files);
    }
    return ragflowRequest.post(
      `/api/v1/datasets/${datasetId}/documents`,
      formData,
      {
        headers: {
          'Authorization': `Bearer ${RAGFLOW_API_KEY}`,
          'Content-Type': 'multipart/form-data'
        }
      }
    );
  }

/**
 * 更新数据集内文档配置
 * @param {string} datasetId - 数据集ID
 * @param {string} documentId - 文档ID
 * @param {Object} data - 要更新的字段，如 { name, meta_fields, chunk_method, parser_config }
 * @returns {Promise}
 */
export function updateDatasetDocument(datasetId, documentId, data) {
  return ragflowRequest.put(
    `/api/v1/datasets/${datasetId}/documents/${documentId}`,
    data,
    {
      headers: {
        'Authorization': `Bearer ${RAGFLOW_API_KEY}`,
        'Content-Type': 'application/json'
      }
    }
  );
}

/**
 * 下载数据集内文档
 * @param {string} datasetId - 数据集ID
 * @param {string} documentId - 文档ID
 * @param {string} responseType - 响应类型，默认 'blob'（可选）
 * @returns {Promise}
 */
export function downloadDatasetDocument(datasetId, documentId, responseType = 'blob') {
  return ragflowRequest.get(
    `/api/v1/datasets/${datasetId}/documents/${documentId}`,
    {
      responseType,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${RAGFLOW_API_KEY}`
      }
    }
  );
}

/**
 * 获取数据集内文档列表
 * @param {string} datasetId - 数据集ID
 * @param {Object} params - 查询参数，如 { page, page_size, orderby, desc, keywords, id, name }
 * @returns {Promise}
 */
export function listDatasetDocuments(datasetId, params = {}) {
  return ragflowRequest.get(
    `/api/v1/datasets/${datasetId}/documents`,
    {
      params,
      headers: {
        'Authorization': `Bearer ${RAGFLOW_API_KEY}`,
        'Content-Type': 'application/json'
      }
    }
  );
}

/**
 * 删除数据集内文档
 * @param {string} datasetId - 数据集ID
 * @param {Array<string>} ids - 要删除的文档ID列表（可选，空则删除全部）
 * @returns {Promise}
 */
export function deleteDatasetDocuments(datasetId, ids = []) {
  return ragflowRequest.delete(
    `/api/v1/datasets/${datasetId}/documents`,
    {
      data: { ids },
      headers: {
        'Authorization': `Bearer ${RAGFLOW_API_KEY}`,
        'Content-Type': 'application/json'
      }
    }
  );
}

/**
 * 解析数据集内文档
 * @param {string} datasetId - 数据集ID
 * @param {Array<string>} documentIds - 要解析的文档ID列表
 * @returns {Promise}
 */
export function parseDatasetDocuments(datasetId, documentIds) {
  return ragflowRequest.post(
    `/api/v1/datasets/${datasetId}/chunks`,
    { document_ids: documentIds },
    {
      headers: {
        'Authorization': `Bearer ${RAGFLOW_API_KEY}`,
        'Content-Type': 'application/json'
      }
    }
  );
}

/**
 * 停止解析数据集内文档
 * @param {string} datasetId - 数据集ID
 * @param {Array<string>} documentIds - 要停止解析的文档ID列表
 * @returns {Promise}
 */
export function stopParseDatasetDocuments(datasetId, documentIds) {
  return ragflowRequest.delete(
    `/api/v1/datasets/${datasetId}/chunks`,
    {
      data: { document_ids: documentIds },
      headers: {
        'Authorization': `Bearer ${RAGFLOW_API_KEY}`,
        'Content-Type': 'application/json'
      }
    }
  );
}

// TODO: 实现相关API函数 